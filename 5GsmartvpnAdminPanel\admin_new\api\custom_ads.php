<?php
/**
 * 5G Smart VPN Admin Panel - Modern Custom Ads API
 * Returns random active custom ads for the mobile app
 */

require_once '../db.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Missing authentication parameters",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Request timestamp expired",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Invalid request signature",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

try {
    $db = getDB();

    // Get current date for filtering active ads
    $today = date("Y-m-d");

    // Query to get active custom ads
    $custom_ads_list = $db->fetchAll(
        "SELECT * FROM custom_ads WHERE `on` = 1 AND date_start <= ? AND date_end >= ?",
        [$today, $today]
    );

    $response_data = [];

    if (count($custom_ads_list) > 0) {
        // If there are running ads, select a random one
        $random_index = array_rand($custom_ads_list);
        $random_ad = $custom_ads_list[$random_index];

        // Add metadata to the ad
        $random_ad['api_version'] = '3.0';
        $random_ad['timestamp'] = time();
        $random_ad['source'] = 'modern_admin_panel';
        $random_ad['endpoint'] = 'custom_ads';
        $random_ad['total_active_ads'] = count($custom_ads_list);

        $response_data[] = $random_ad;
    } else {
        // If no running ads, return empty array with metadata
        $response_data = [
            'message' => 'No active custom ads found',
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => 0
        ];
    }

    echo json_encode($response_data);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "error" => "Internal Server Error",
        "message" => "Failed to fetch custom ads",
        "code" => 500,
        "timestamp" => time(),
        "debug" => $e->getMessage()
    ]);
}
?>
