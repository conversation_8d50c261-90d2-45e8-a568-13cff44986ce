package com.official.fivegfastvpn.model;

/**
 * Custom Ad Model for 5G Smart VPN
 * Represents a custom advertisement from the admin panel
 */
public class CustomAd {
    private int id;
    private String title;
    private String image;
    private String text;
    private String url;
    private String dateStart;
    private String dateEnd;
    private int on; // 1 = active, 0 = inactive
    private int viewCount;
    private int clickCount;
    
    // Metadata fields
    private String apiVersion;
    private long timestamp;
    private String source;
    private String endpoint;
    private int totalActiveAds;
    
    // Constructors
    public CustomAd() {}
    
    public CustomAd(int id, String title, String text, String url) {
        this.id = id;
        this.title = title;
        this.text = text;
        this.url = url;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getImage() {
        return image;
    }
    
    public void setImage(String image) {
        this.image = image;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getDateStart() {
        return dateStart;
    }
    
    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }
    
    public String getDateEnd() {
        return dateEnd;
    }
    
    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }
    
    public int getOn() {
        return on;
    }
    
    public void setOn(int on) {
        this.on = on;
    }
    
    public int getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(int viewCount) {
        this.viewCount = viewCount;
    }
    
    public int getClickCount() {
        return clickCount;
    }
    
    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public int getTotalActiveAds() {
        return totalActiveAds;
    }
    
    public void setTotalActiveAds(int totalActiveAds) {
        this.totalActiveAds = totalActiveAds;
    }
    
    /**
     * Check if the ad is active
     * @return true if ad is active
     */
    public boolean isActive() {
        return on == 1;
    }
    
    /**
     * Get the full image URL
     * @param baseUrl Base URL for images
     * @return Full image URL
     */
    public String getFullImageUrl(String baseUrl) {
        if (image == null || image.isEmpty()) {
            return null;
        }
        
        // If image already contains full URL, return as is
        if (image.startsWith("http://") || image.startsWith("https://")) {
            return image;
        }
        
        // Construct full URL
        return baseUrl + "/" + image;
    }
    
    /**
     * Get click-through rate
     * @return CTR as percentage
     */
    public double getClickThroughRate() {
        if (viewCount == 0) {
            return 0.0;
        }
        return (double) clickCount / viewCount * 100.0;
    }
    
    @Override
    public String toString() {
        return "CustomAd{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", image='" + image + '\'' +
                ", text='" + text + '\'' +
                ", url='" + url + '\'' +
                ", dateStart='" + dateStart + '\'' +
                ", dateEnd='" + dateEnd + '\'' +
                ", on=" + on +
                ", viewCount=" + viewCount +
                ", clickCount=" + clickCount +
                ", apiVersion='" + apiVersion + '\'' +
                ", timestamp=" + timestamp +
                ", source='" + source + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", totalActiveAds=" + totalActiveAds +
                '}';
    }
}
