<?php
/**
 * 5G Smart VPN Admin Panel - Modern Settings API
 * Returns app settings from the modern settings table
 */

require_once '../db.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Missing authentication parameters",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Request timestamp expired",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Invalid request signature",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

try {
    // Get query parameters
    $category = $_GET['category'] ?? 'all'; // 'all', 'app', 'ads', 'system'
    $keys = $_GET['keys'] ?? null; // Comma-separated list of specific keys

    // Build query based on category
    $query = "SELECT setting_key, setting_value FROM settings WHERE 1=1";
    $params = [];
    $types = '';

    if ($category !== 'all') {
        switch ($category) {
            case 'app':
                $query .= " AND (setting_key LIKE 'app_%' OR setting_key IN ('maintenance_mode', 'debug_mode', 'auto_connect', 'kill_switch', 'dns_leak_protection'))";
                break;
            case 'ads':
                $query .= " AND (setting_key LIKE 'admob_%' OR setting_key LIKE 'facebook_%' OR setting_key LIKE '%_type' OR setting_key LIKE '%_enabled' OR setting_key IN ('test_mode', 'click_limit', 'show_frequency', 'reward_time'))";
                break;
            case 'system':
                $query .= " AND (setting_key LIKE 'smtp_%' OR setting_key IN ('notification_email', 'backup_frequency', 'log_level', 'connection_timeout', 'max_concurrent_connections'))";
                break;
            default:
                // Invalid category, return all
                break;
        }
    }

    // Filter by specific keys if provided
    if ($keys) {
        $keyArray = array_map('trim', explode(',', $keys));
        $placeholders = str_repeat('?,', count($keyArray) - 1) . '?';
        $query .= " AND setting_key IN ($placeholders)";
        $params = array_merge($params, $keyArray);
        $types .= str_repeat('s', count($keyArray));
    }

    // Add ordering
    $query .= " ORDER BY setting_key ASC";

    $db = getDB();

    // Execute query
    $result = $db->fetchAll($query, $params);

    $settings = [];
    foreach ($result as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Get total settings count for metadata
    $totalCount = $db->fetchOne("SELECT COUNT(*) as total FROM settings")['total'];

    // Build response
    $response = [
        'success' => true,
        'settings' => $settings,
        'metadata' => [
            'total_settings' => (int)$totalCount,
            'returned_settings' => count($settings),
            'category' => $category,
            'filtered_keys' => $keys ? explode(',', $keys) : null,
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'settings'
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "error" => "Internal Server Error",
        "message" => "Failed to fetch settings",
        "code" => 500,
        "timestamp" => time(),
        "debug" => $e->getMessage()
    ]);
}
?>
