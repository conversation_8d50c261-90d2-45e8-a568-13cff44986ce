<?php
/**
 * 5G Smart VPN Admin Panel - Modern Servers API
 * Returns active VPN servers list
 */

require_once '../db.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Missing authentication parameters",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Request timestamp expired",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Invalid request signature",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

try {
    // Get query parameters
    $status = $_GET['status'] ?? '1'; // Default to active servers
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;
    $orderBy = $_GET['order'] ?? 'pos'; // Default order by position
    $orderDir = $_GET['dir'] ?? 'DESC'; // Default descending

    // Validate parameters
    $allowedOrderBy = ['id', 'name', 'pos', 'status'];
    $allowedOrderDir = ['ASC', 'DESC'];

    if (!in_array($orderBy, $allowedOrderBy)) {
        $orderBy = 'pos';
    }

    if (!in_array($orderDir, $allowedOrderDir)) {
        $orderDir = 'DESC';
    }

    // Build query
    $query = "SELECT * FROM servers WHERE `status` = ?";
    $params = [$status];
    $types = 'i';

    // Add ordering
    $query .= " ORDER BY `{$orderBy}` {$orderDir}";

    // Add limit if specified
    if ($limit && $limit > 0) {
        $query .= " LIMIT ?";
        $params[] = $limit;
        $types .= 'i';
    }

    $db = getDB();

    // Execute query
    $servers = $db->fetchAll($query, $params);

    // Get total count for metadata
    $totalCount = $db->fetchOne("SELECT COUNT(*) as total FROM servers WHERE status = ?", [$status])['total'];

    // Build response
    $response = [
        'success' => true,
        'servers' => $servers,
        'metadata' => [
            'total_servers' => (int)$totalCount,
            'returned_servers' => count($servers),
            'status_filter' => (int)$status,
            'order_by' => $orderBy,
            'order_direction' => $orderDir,
            'limit' => $limit,
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'servers'
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "error" => "Internal Server Error",
        "message" => "Failed to fetch servers",
        "code" => 500,
        "timestamp" => time(),
        "debug" => $e->getMessage()
    ]);
}
?>
