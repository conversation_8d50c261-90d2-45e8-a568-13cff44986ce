<?php
/**
 * 5G Smart VPN Admin Panel - Modern Configuration API
 * Serves app configuration including ads and servers from modern settings table
 */

require_once '../db.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Missing authentication parameters",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Request timestamp expired",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Invalid request signature",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

// Get app configuration API
if (isset($_GET['pkg'])) {

    try {
        $db = getDB();

        // Get settings from the modern settings table
        $settings_query = "SELECT setting_key, setting_value FROM settings WHERE
            setting_key LIKE 'admob_%' OR
            setting_key LIKE 'facebook_%' OR
            setting_key LIKE '%_type' OR
            setting_key IN ('banner_enabled', 'interstitial_enabled', 'rewarded_enabled', 'native_enabled', 'openad_enabled', 'test_mode', 'click_limit', 'show_frequency', 'reward_time')";

        $settings_result = $db->fetchAll($settings_query);

        $settings = [];
        foreach ($settings_result as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        // Get active servers
        $servers_list = $db->fetchAll("SELECT * FROM servers WHERE status = 1 ORDER BY pos DESC");

        // Build response data in format expected by mobile app
        $response_data = [];
        $config = [];

        // AdMob Configuration
        $config['admob_id'] = $settings['admob_app_id'] ?? '';
        $config['admob_banner'] = $settings['admob_banner_id'] ?? '';
        $config['admob_interstitial'] = $settings['admob_interstitial_id'] ?? '';
        $config['admob_native'] = $settings['admob_native_id'] ?? '';
        $config['admob_rewarded'] = $settings['admob_rewarded_id'] ?? '';
        $config['admob_openad'] = $settings['admob_openad_id'] ?? '';

        // Facebook Ads Configuration
        $config['facebook_id'] = $settings['facebook_app_id'] ?? '';
        $config['facebook_banner'] = $settings['facebook_banner_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_interstitial'] = $settings['facebook_interstitial_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_native'] = $settings['facebook_native_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_rewarded'] = $settings['facebook_rewarded_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';

        // Ad Status and Types
        $config['ads_status'] = (
            $settings['banner_enabled'] == '1' ||
            $settings['interstitial_enabled'] == '1' ||
            $settings['rewarded_enabled'] == '1' ||
            $settings['native_enabled'] == '1' ||
            $settings['openad_enabled'] == '1'
        ) ? 1 : 0;

        $config['banner_type'] = $settings['banner_type'] ?? 'admob';
        $config['interstitial_type'] = $settings['interstitial_type'] ?? 'admob';
        $config['native_type'] = $settings['native_type'] ?? 'admob';
        $config['rewarded_type'] = $settings['rewarded_type'] ?? 'admob';

        // Additional Settings
        $config['reward_time'] = (int)($settings['reward_time'] ?? 30);
        $config['click_limit'] = (int)($settings['click_limit'] ?? 5);
        $config['show_frequency'] = (int)($settings['show_frequency'] ?? 3);
        $config['test_mode'] = (int)($settings['test_mode'] ?? 1);

        // Individual ad type enabled status (for backward compatibility)
        $config['banner_enabled'] = (int)($settings['banner_enabled'] ?? 1);
        $config['interstitial_enabled'] = (int)($settings['interstitial_enabled'] ?? 1);
        $config['rewarded_enabled'] = (int)($settings['rewarded_enabled'] ?? 0);
        $config['native_enabled'] = (int)($settings['native_enabled'] ?? 0);
        $config['openad_enabled'] = (int)($settings['openad_enabled'] ?? 1);

        // Legacy compatibility fields
        $config['app_id'] = $config['admob_id']; // Legacy field name
        $config['banner'] = $config['admob_banner']; // Legacy field name
        $config['interstitial'] = $config['admob_interstitial']; // Legacy field name
        $config['native'] = $config['admob_native']; // Legacy field name
        $config['rewarded'] = $config['admob_rewarded']; // Legacy field name
        $config['openad'] = $config['admob_openad']; // Legacy field name
        $config['active'] = $config['ads_status']; // Legacy field name

        // Add servers to configuration
        $config['servers'] = $servers_list;

        // Add metadata
        $config['api_version'] = '3.0';
        $config['timestamp'] = time();
        $config['source'] = 'modern_admin_panel';
        $config['endpoint'] = 'config';

        $response_data[] = $config;

        // Return JSON response
        echo json_encode($response_data);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            "error" => "Internal Server Error",
            "message" => "Failed to fetch configuration",
            "code" => 500,
            "timestamp" => time(),
            "debug" => $e->getMessage()
        ]);
    }

} else {
    http_response_code(400);
    echo json_encode([
        "error" => "Bad Request",
        "message" => "Missing required parameter: pkg",
        "code" => 400,
        "timestamp" => time()
    ]);
}
?>
