<?php
/**
 * Analytics Integration Test Script
 * Tests the complete flow from Android app to analytics dashboard
 */

require_once 'db.php';
require_once 'config.php';

echo "<h1>5G Smart VPN - Analytics Integration Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>\n";

// Test 1: Database Connection and Tables
echo "<div class='test-section'>\n";
echo "<h2>1. Database Connection and Table Structure</h2>\n";

try {
    $db = getDB();
    echo "<p class='success'>✅ Database connection successful</p>\n";
    
    // Check if ad_tracking table exists
    $tables = $db->query("SHOW TABLES LIKE 'ad_tracking'")->fetchAll();
    if (count($tables) > 0) {
        echo "<p class='success'>✅ ad_tracking table exists</p>\n";
        
        // Show table structure
        $structure = $db->query("DESCRIBE ad_tracking")->fetchAll();
        echo "<h3>ad_tracking Table Structure:</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($structure as $column) {
            echo "<tr>";
            foreach ($column as $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p class='warning'>⚠️ ad_tracking table does not exist - it will be created when first tracking event occurs</p>\n";
    }
    
    // Check custom_ads table
    $custom_ads = $db->query("SELECT COUNT(*) as count FROM custom_ads WHERE `on` = 1")->fetch();
    echo "<p class='info'>ℹ️ Active custom ads: " . $custom_ads['count'] . "</p>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>\n";
}
echo "</div>\n";

// Test 2: API Authentication
echo "<div class='test-section'>\n";
echo "<h2>2. API Authentication Test</h2>\n";

$timestamp = time();
$api_key = API_KEY;
$signature = hash_hmac('sha256', $timestamp, $api_key);

echo "<p class='info'>API Key: " . substr($api_key, 0, 10) . "...</p>\n";
echo "<p class='info'>Timestamp: $timestamp</p>\n";
echo "<p class='info'>Signature: " . substr($signature, 0, 20) . "...</p>\n";

// Test API endpoint
$test_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/analytics.php?action=summary&timestamp=$timestamp&signature=$signature";
echo "<p class='info'>Test URL: <a href='$test_url' target='_blank'>$test_url</a></p>\n";

echo "</div>\n";

// Test 3: Simulate Android App Tracking
echo "<div class='test-section'>\n";
echo "<h2>3. Simulate Android App Ad Tracking</h2>\n";

try {
    // Simulate custom ad view
    $track_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/track.php";
    $track_timestamp = time();
    $track_signature = hash_hmac('sha256', $track_timestamp, $api_key);
    
    // Simulate tracking data
    $tracking_data = [
        'ad_type' => 'custom',
        'ad_id' => 1,
        'event_type' => 'view',
        'user_id' => 'test_user_' . rand(1000, 9999),
        'timestamp' => $track_timestamp,
        'signature' => $track_signature
    ];
    
    echo "<h3>Simulated Tracking Data:</h3>\n";
    echo "<pre>" . json_encode($tracking_data, JSON_PRETTY_PRINT) . "</pre>\n";
    
    // Test tracking endpoint
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $track_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($tracking_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response !== false) {
        echo "<p class='info'>HTTP Response Code: $http_code</p>\n";
        echo "<h3>Tracking API Response:</h3>\n";
        echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
        
        if ($http_code == 200) {
            echo "<p class='success'>✅ Tracking API responded successfully</p>\n";
        } else {
            echo "<p class='warning'>⚠️ Tracking API returned HTTP $http_code</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Failed to connect to tracking API</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Tracking simulation error: " . $e->getMessage() . "</p>\n";
}

echo "</div>\n";

// Test 4: Check Tracking Data in Database
echo "<div class='test-section'>\n";
echo "<h2>4. Tracking Data in Database</h2>\n";

try {
    // Check if tracking data exists
    $tracking_count = $db->query("SELECT COUNT(*) as count FROM ad_tracking")->fetch();
    echo "<p class='info'>Total tracking records: " . $tracking_count['count'] . "</p>\n";
    
    if ($tracking_count['count'] > 0) {
        // Show recent tracking data
        $recent_tracking = $db->query("
            SELECT ad_type, ad_id, event_type, user_id, created_at 
            FROM ad_tracking 
            ORDER BY created_at DESC 
            LIMIT 10
        ")->fetchAll();
        
        echo "<h3>Recent Tracking Data:</h3>\n";
        echo "<table>\n";
        echo "<tr><th>Ad Type</th><th>Ad ID</th><th>Event Type</th><th>User ID</th><th>Created At</th></tr>\n";
        foreach ($recent_tracking as $track) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($track['ad_type']) . "</td>";
            echo "<td>" . htmlspecialchars($track['ad_id']) . "</td>";
            echo "<td>" . htmlspecialchars($track['event_type']) . "</td>";
            echo "<td>" . htmlspecialchars($track['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($track['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Show analytics summary
        $analytics_summary = $db->query("
            SELECT 
                ad_type,
                event_type,
                COUNT(*) as count,
                DATE(created_at) as date
            FROM ad_tracking 
            WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
            GROUP BY ad_type, event_type, DATE(created_at)
            ORDER BY date DESC, ad_type, event_type
        ")->fetchAll();
        
        if (count($analytics_summary) > 0) {
            echo "<h3>Analytics Summary (Last 7 Days):</h3>\n";
            echo "<table>\n";
            echo "<tr><th>Date</th><th>Ad Type</th><th>Event Type</th><th>Count</th></tr>\n";
            foreach ($analytics_summary as $summary) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($summary['date']) . "</td>";
                echo "<td>" . htmlspecialchars($summary['ad_type']) . "</td>";
                echo "<td>" . htmlspecialchars($summary['event_type']) . "</td>";
                echo "<td>" . htmlspecialchars($summary['count']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } else {
        echo "<p class='warning'>⚠️ No tracking data found. This could mean:</p>\n";
        echo "<ul>\n";
        echo "<li>Android app hasn't sent any tracking data yet</li>\n";
        echo "<li>Tracking API endpoint is not working</li>\n";
        echo "<li>Database table hasn't been created yet</li>\n";
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database query error: " . $e->getMessage() . "</p>\n";
}

echo "</div>\n";

// Test 5: Analytics API Test
echo "<div class='test-section'>\n";
echo "<h2>5. Analytics API Test</h2>\n";

try {
    $analytics_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/analytics.php";
    $analytics_timestamp = time();
    $analytics_signature = hash_hmac('sha256', $analytics_timestamp, $api_key);
    
    $test_endpoints = [
        'summary' => "$analytics_url?action=summary&timestamp=$analytics_timestamp&signature=$analytics_signature",
        'daily' => "$analytics_url?action=daily&timestamp=$analytics_timestamp&signature=$analytics_signature",
        'custom_ads' => "$analytics_url?action=custom_ads&timestamp=$analytics_timestamp&signature=$analytics_signature",
        'real_time' => "$analytics_url?action=real_time&timestamp=$analytics_timestamp&signature=$analytics_signature"
    ];
    
    foreach ($test_endpoints as $action => $url) {
        echo "<h3>Testing $action endpoint:</h3>\n";
        echo "<p class='info'>URL: <a href='$url' target='_blank'>$url</a></p>\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response !== false) {
            echo "<p class='info'>HTTP Response Code: $http_code</p>\n";
            
            if ($http_code == 200) {
                $data = json_decode($response, true);
                if ($data) {
                    echo "<p class='success'>✅ $action endpoint working</p>\n";
                    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>\n";
                } else {
                    echo "<p class='error'>❌ Invalid JSON response</p>\n";
                    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
                }
            } else {
                echo "<p class='error'>❌ HTTP error $http_code</p>\n";
                echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
            }
        } else {
            echo "<p class='error'>❌ Failed to connect to analytics API</p>\n";
        }
        
        echo "<hr>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Analytics API test error: " . $e->getMessage() . "</p>\n";
}

echo "</div>\n";

// Test 6: Integration Recommendations
echo "<div class='test-section'>\n";
echo "<h2>6. Integration Status and Recommendations</h2>\n";

echo "<h3>✅ Completed Integrations:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Custom ad tracking in MainActivity</li>\n";
echo "<li>✅ Real-time analytics API endpoint</li>\n";
echo "<li>✅ Analytics dashboard with real data integration</li>\n";
echo "<li>✅ HMAC authentication for API security</li>\n";
echo "</ul>\n";

echo "<h3>📋 Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Test Android App:</strong> Run the Android app and trigger custom ads to generate tracking data</li>\n";
echo "<li><strong>Monitor Logs:</strong> Check Android Studio logcat for tracking API calls</li>\n";
echo "<li><strong>Verify Dashboard:</strong> Visit <a href='ad-analytics.php' target='_blank'>ad-analytics.php</a> to see real-time data</li>\n";
echo "<li><strong>Test API Endpoints:</strong> Use the URLs above to test each analytics endpoint</li>\n";
echo "<li><strong>Add More Ad Types:</strong> Integrate AdMob and Facebook ad tracking</li>\n";
echo "</ol>\n";

echo "<h3>🔧 Debugging Tips:</h3>\n";
echo "<ul>\n";
echo "<li>Check Android Studio logcat for 'AdTracker', 'MainActivity', and 'VpnApiService' tags</li>\n";
echo "<li>Verify network connectivity between Android device and server</li>\n";
echo "<li>Test API endpoints manually using the URLs provided above</li>\n";
echo "<li>Check database for tracking data after triggering ads in the app</li>\n";
echo "<li>Monitor browser console for real-time analytics updates</li>\n";
echo "</ul>\n";

echo "</div>\n";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
