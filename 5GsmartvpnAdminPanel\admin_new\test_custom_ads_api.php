<?php
/**
 * Test script for Custom Ads API
 * This script tests the custom ads API endpoint directly
 */

require_once 'db.php';

echo "<h1>5G Smart VPN - Custom Ads API Test</h1>\n";
echo "<h2>Testing Custom Ads API Endpoint</h2>\n";

// Test 1: Check database connection
echo "<h3>1. Database Connection Test</h3>\n";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>\n";
    exit;
}

// Test 2: Check custom ads in database
echo "<h3>2. Custom Ads in Database</h3>\n";
try {
    $today = date("Y-m-d");
    echo "Today's date: $today<br>\n";
    
    $all_ads = $db->fetchAll("SELECT * FROM custom_ads ORDER BY id");
    echo "Total ads in database: " . count($all_ads) . "<br>\n";
    
    foreach ($all_ads as $ad) {
        $status = $ad['on'] ? 'Active' : 'Inactive';
        $date_status = ($ad['date_start'] <= $today && $ad['date_end'] >= $today) ? 'Current' : 'Expired/Future';
        echo "- Ad #{$ad['id']}: {$ad['title']} | Status: $status | Date: $date_status ({$ad['date_start']} to {$ad['date_end']})<br>\n";
    }
    
    $active_ads = $db->fetchAll(
        "SELECT * FROM custom_ads WHERE `on` = 1 AND date_start <= ? AND date_end >= ?",
        [$today, $today]
    );
    echo "<br>Active ads for today: " . count($active_ads) . "<br>\n";
    
} catch (Exception $e) {
    echo "❌ Database query failed: " . $e->getMessage() . "<br>\n";
}

// Test 3: Test API authentication
echo "<h3>3. API Authentication Test</h3>\n";
$timestamp = time();
$api_key = API_KEY;
$signature = hash_hmac('sha256', $timestamp, $api_key);

echo "API Key: " . substr($api_key, 0, 10) . "...<br>\n";
echo "Timestamp: $timestamp<br>\n";
echo "Signature: " . substr($signature, 0, 20) . "...<br>\n";

// Test 4: Simulate API call
echo "<h3>4. Simulate API Response</h3>\n";
try {
    $today = date("Y-m-d");
    $custom_ads_list = $db->fetchAll(
        "SELECT * FROM custom_ads WHERE `on` = 1 AND date_start <= ? AND date_end >= ?",
        [$today, $today]
    );

    if (count($custom_ads_list) > 0) {
        $random_index = array_rand($custom_ads_list);
        $random_ad = $custom_ads_list[$random_index];

        // Normalize field names for consistency with Android app
        $normalized_ad = [
            'id' => $random_ad['id'],
            'title' => $random_ad['title'],
            'image' => $random_ad['image'],
            'text' => $random_ad['text'],
            'url' => $random_ad['url'],
            'date_start' => $random_ad['date_start'],
            'date_end' => $random_ad['date_end'],
            'on' => $random_ad['on'],
            'view_count' => $random_ad['view_count'],
            'click_count' => $random_ad['click_count'],
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => count($custom_ads_list)
        ];

        echo "✅ API would return the following ad:<br>\n";
        echo "<pre>" . json_encode([$normalized_ad], JSON_PRETTY_PRINT) . "</pre>\n";
    } else {
        echo "❌ No active custom ads found for today<br>\n";
        echo "Response would be:<br>\n";
        $response = [
            'message' => 'No active custom ads found',
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => 0
        ];
        echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>\n";
    }

} catch (Exception $e) {
    echo "❌ API simulation failed: " . $e->getMessage() . "<br>\n";
}

// Test 5: Provide API URL for testing
echo "<h3>5. API URL for Testing</h3>\n";
$base_url = "http://*************/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/custom_ads.php";
$test_url = $base_url . "?timestamp=$timestamp&signature=$signature";
echo "Test URL: <a href='$test_url' target='_blank'>$test_url</a><br>\n";
echo "You can test this URL directly in your browser or with curl.<br>\n";

echo "<h3>6. Recommendations</h3>\n";
if (count($active_ads ?? []) == 0) {
    echo "❌ No active ads found. Please:<br>\n";
    echo "1. Run the SQL script: add_test_custom_ad.sql<br>\n";
    echo "2. Or manually add/update custom ads in the admin panel<br>\n";
    echo "3. Ensure ads have current date ranges<br>\n";
} else {
    echo "✅ Active ads found. The API should work correctly.<br>\n";
}

echo "<br><strong>Next steps:</strong><br>\n";
echo "1. Test the API URL above in your browser<br>\n";
echo "2. Run the Android app and check logs<br>\n";
echo "3. Use ApiTestActivity to test the integration<br>\n";
?>
