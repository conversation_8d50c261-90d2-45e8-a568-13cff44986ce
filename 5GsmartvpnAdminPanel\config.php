<?php
/**
 * 5G Smart VPN Admin Panel - Main Configuration
 * Database connection and global settings
 */

// Set timezone
date_default_timezone_set('Asia/Dhaka');

// Error reporting (enable for debugging)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Configuration
$host = "localhost";
$user = "root";
$pass = "";
$db = "5gsmartvpnnewupdate";

// Create database connection
$conn = mysqli_connect($host, $user, $pass, $db);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Set charset
mysqli_set_charset($conn, 'utf8mb4');

// API Configuration
$API_KEY = '5g-smart-vpn-api-key-2024-secure'; // Updated to match the new key

// Global functions
function logError($message, $file = __FILE__, $line = __LINE__) {
    error_log("[" . date('Y-m-d H:i:s') . "] Error in $file:$line - $message");
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}


