<?php
/**
 * Test Tracking API
 * Simulates Android app sending tracking data
 */

require_once 'includes/config.php';

echo "<h1>Test Tracking API</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test 1: Create tracking table by sending a test event
echo "<h2>1. Test Tracking API</h2>";

$timestamp = time();
$signature = hash_hmac('sha256', $timestamp, $API_KEY);

$test_data = [
    'ad_type' => 'custom',
    'ad_id' => 1,
    'event_type' => 'view',
    'user_id' => 'test_user_' . rand(1000, 9999),
    'device_info' => [
        'model' => 'Test Device',
        'os_version' => 'Android 12',
        'app_version' => '1.0.0'
    ],
    'timestamp' => $timestamp,
    'signature' => $signature
];

echo "<h3>Sending test tracking data:</h3>";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>";

// Send POST request to tracking API
$url = 'http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/track.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($test_data))
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "<h3>API Response:</h3>";
echo "<p>HTTP Code: $http_code</p>";

if ($curl_error) {
    echo "<p class='error'>cURL Error: $curl_error</p>";
} else {
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    if ($http_code == 200) {
        echo "<p class='success'>✅ Tracking API working successfully!</p>";
        
        // Test 2: Send a click event
        echo "<h2>2. Test Click Tracking</h2>";
        
        $click_data = $test_data;
        $click_data['event_type'] = 'click';
        $click_data['user_id'] = 'test_user_' . rand(1000, 9999);
        $click_data['timestamp'] = time();
        $click_data['signature'] = hash_hmac('sha256', $click_data['timestamp'], $API_KEY);
        
        $ch2 = curl_init();
        curl_setopt($ch2, CURLOPT_URL, $url);
        curl_setopt($ch2, CURLOPT_POST, true);
        curl_setopt($ch2, CURLOPT_POSTFIELDS, json_encode($click_data));
        curl_setopt($ch2, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($click_data))
        ]);
        curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch2, CURLOPT_TIMEOUT, 10);
        
        $response2 = curl_exec($ch2);
        $http_code2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
        curl_close($ch2);
        
        echo "<p>Click tracking response (HTTP $http_code2):</p>";
        echo "<pre>" . htmlspecialchars($response2) . "</pre>";
        
    } else {
        echo "<p class='error'>❌ Tracking API failed with HTTP $http_code</p>";
    }
}

// Test 3: Check if ad_tracking table was created
echo "<h2>3. Check Database</h2>";

if ($conn) {
    // Check if table exists now
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'ad_tracking'");
    if (mysqli_num_rows($table_check) > 0) {
        echo "<p class='success'>✅ ad_tracking table created successfully!</p>";
        
        // Show table structure
        $structure = mysqli_query($conn, "DESCRIBE ad_tracking");
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = mysqli_fetch_assoc($structure)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show recent tracking data
        $recent_data = mysqli_query($conn, "SELECT * FROM ad_tracking ORDER BY created_at DESC LIMIT 5");
        if (mysqli_num_rows($recent_data) > 0) {
            echo "<h3>Recent Tracking Data:</h3>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Ad Type</th><th>Ad ID</th><th>Event Type</th><th>User ID</th><th>Created At</th></tr>";
            while ($row = mysqli_fetch_assoc($recent_data)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['ad_type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['ad_id'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($row['event_type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['user_id'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check updated custom_ads data
        echo "<h3>Updated Custom Ads Data:</h3>";
        $custom_ads = mysqli_query($conn, "SELECT id, title, view_count, click_count FROM custom_ads ORDER BY id");
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Title</th><th>View Count</th><th>Click Count</th></tr>";
        while ($row = mysqli_fetch_assoc($custom_ads)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['title']) . "</td>";
            echo "<td>" . htmlspecialchars($row['view_count']) . "</td>";
            echo "<td>" . htmlspecialchars($row['click_count']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p class='error'>❌ ad_tracking table was not created</p>";
    }
} else {
    echo "<p class='error'>❌ Database connection failed</p>";
}

// Test 4: Test Analytics API
echo "<h2>4. Test Analytics API</h2>";

$analytics_timestamp = time();
$analytics_signature = hash_hmac('sha256', $analytics_timestamp, $API_KEY);
$analytics_url = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/api/analytics.php?action=summary&timestamp=$analytics_timestamp&signature=$analytics_signature";

echo "<p>Testing analytics API: <a href='$analytics_url' target='_blank'>$analytics_url</a></p>";

$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, $analytics_url);
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_TIMEOUT, 10);

$analytics_response = curl_exec($ch3);
$analytics_http_code = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "<p>Analytics API Response (HTTP $analytics_http_code):</p>";
echo "<pre>" . htmlspecialchars($analytics_response) . "</pre>";

if ($analytics_http_code == 200) {
    echo "<p class='success'>✅ Analytics API working!</p>";
} else {
    echo "<p class='error'>❌ Analytics API failed</p>";
}

echo "<h2>5. Next Steps</h2>";
echo "<p>Now try accessing the analytics dashboards:</p>";
echo "<ul>";
echo "<li><a href='simple_analytics.php' target='_blank'>Simple Analytics Dashboard</a></li>";
echo "<li><a href='ad-analytics.php' target='_blank'>Full Analytics Dashboard</a></li>";
echo "</ul>";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
