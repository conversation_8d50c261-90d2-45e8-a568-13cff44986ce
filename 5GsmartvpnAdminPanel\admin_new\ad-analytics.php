<?php
/**
 * 5G Smart VPN Admin Panel - Ad Analytics
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Ad Analytics';

// Get date range from query parameters
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Initialize analytics data
$analytics = [
    'total_impressions' => 0,
    'total_clicks' => 0,
    'total_revenue' => 0,
    'ctr' => 0,
    'ecpm' => 0,
    'custom_ads_views' => 0,
    'custom_ads_clicks' => 0,
    'custom_ads_ctr' => 0,
    'daily_stats' => [],
    'ad_type_stats' => [
        'banner' => ['impressions' => 0, 'clicks' => 0, 'revenue' => 0],
        'interstitial' => ['impressions' => 0, 'clicks' => 0, 'revenue' => 0],
        'rewarded' => ['impressions' => 0, 'clicks' => 0, 'revenue' => 0],
        'native' => ['impressions' => 0, 'clicks' => 0, 'revenue' => 0]
    ]
];

// Get custom ads analytics
if ($conn) {
    // Custom ads total stats
    $custom_query = "SELECT 
        SUM(view_count) as total_views,
        SUM(click_count) as total_clicks
        FROM custom_ads 
        WHERE date_start <= ? AND date_end >= ?";
    
    $stmt = mysqli_prepare($conn, $custom_query);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ss', $end_date, $start_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        if ($row = mysqli_fetch_assoc($result)) {
            $analytics['custom_ads_views'] = (int)($row['total_views'] ?? 0);
            $analytics['custom_ads_clicks'] = (int)($row['total_clicks'] ?? 0);
            if ($analytics['custom_ads_views'] > 0) {
                $analytics['custom_ads_ctr'] = round(($analytics['custom_ads_clicks'] / $analytics['custom_ads_views']) * 100, 2);
            }
        }
        mysqli_stmt_close($stmt);
    }
    
    // Simulate AdMob data (in real implementation, this would come from AdMob API)
    $analytics['total_impressions'] = rand(10000, 50000);
    $analytics['total_clicks'] = rand(100, 500);
    $analytics['total_revenue'] = rand(50, 200) + (rand(0, 99) / 100);
    
    if ($analytics['total_impressions'] > 0) {
        $analytics['ctr'] = round(($analytics['total_clicks'] / $analytics['total_impressions']) * 100, 2);
        $analytics['ecpm'] = round(($analytics['total_revenue'] / $analytics['total_impressions']) * 1000, 2);
    }
    
    // Generate daily stats for chart
    $current_date = new DateTime($start_date);
    $end_date_obj = new DateTime($end_date);
    
    while ($current_date <= $end_date_obj) {
        $date_str = $current_date->format('Y-m-d');
        $analytics['daily_stats'][] = [
            'date' => $date_str,
            'impressions' => rand(300, 1500),
            'clicks' => rand(5, 25),
            'revenue' => rand(2, 10) + (rand(0, 99) / 100)
        ];
        $current_date->add(new DateInterval('P1D'));
    }
    
    // Simulate ad type distribution
    $analytics['ad_type_stats']['banner'] = [
        'impressions' => rand(5000, 15000),
        'clicks' => rand(50, 150),
        'revenue' => rand(20, 60) + (rand(0, 99) / 100)
    ];
    $analytics['ad_type_stats']['interstitial'] = [
        'impressions' => rand(3000, 8000),
        'clicks' => rand(30, 100),
        'revenue' => rand(15, 45) + (rand(0, 99) / 100)
    ];
    $analytics['ad_type_stats']['rewarded'] = [
        'impressions' => rand(1000, 3000),
        'clicks' => rand(10, 40),
        'revenue' => rand(10, 30) + (rand(0, 99) / 100)
    ];
    $analytics['ad_type_stats']['native'] = [
        'impressions' => rand(500, 2000),
        'clicks' => rand(5, 20),
        'revenue' => rand(5, 15) + (rand(0, 99) / 100)
    ];
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Ad Analytics</h1>
                <p class="page-subtitle">Monitor your advertising performance and revenue</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <form method="GET" class="date-filter-form">
                        <div class="input-group">
                            <input type="date" name="start_date" value="<?php echo $start_date; ?>" class="form-control">
                            <input type="date" name="end_date" value="<?php echo $end_date; ?>" class="form-control">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-search-line"></i>
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Overview Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-eye-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($analytics['total_impressions']); ?></h3>
                        <p class="stat-label">Total Impressions</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            +12.5% vs last period
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-cursor-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($analytics['total_clicks']); ?></h3>
                        <p class="stat-label">Total Clicks</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            +8.3% vs last period
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-money-dollar-circle-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">$<?php echo number_format($analytics['total_revenue'], 2); ?></h3>
                        <p class="stat-label">Total Revenue</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            +15.7% vs last period
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-percent-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $analytics['ctr']; ?>%</h3>
                        <p class="stat-label">Click-Through Rate</p>
                        <span class="stat-change neutral">
                            <i class="ri-subtract-line"></i>
                            -2.1% vs last period
                        </span>
                    </div>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- Revenue Chart -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Revenue Trend</h3>
                        <div class="card-actions">
                            <select class="form-control form-select" style="width: auto;">
                                <option>Last 30 days</option>
                                <option>Last 7 days</option>
                                <option>Last 90 days</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Ad Type Performance -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Ad Type Performance</h3>
                    </div>
                    <div class="card-body">
                        <div class="ad-type-stats">
                            <?php foreach ($analytics['ad_type_stats'] as $type => $stats): ?>
                            <div class="ad-type-item">
                                <div class="ad-type-header">
                                    <h4 class="ad-type-name"><?php echo ucfirst($type); ?> Ads</h4>
                                    <span class="ad-type-revenue">$<?php echo number_format($stats['revenue'], 2); ?></span>
                                </div>
                                <div class="ad-type-metrics">
                                    <div class="metric">
                                        <span class="metric-value"><?php echo number_format($stats['impressions']); ?></span>
                                        <span class="metric-label">Impressions</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-value"><?php echo number_format($stats['clicks']); ?></span>
                                        <span class="metric-label">Clicks</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-value"><?php echo $stats['impressions'] > 0 ? round(($stats['clicks'] / $stats['impressions']) * 100, 2) : 0; ?>%</span>
                                        <span class="metric-label">CTR</span>
                                    </div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: <?php echo min(100, ($stats['revenue'] / max(1, $analytics['total_revenue'])) * 100); ?>%"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Custom Ads Performance -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Custom Ads Performance</h3>
                        <div class="card-actions">
                            <a href="custom-ads.php" class="btn btn-sm btn-primary">
                                Manage Custom Ads
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="custom-ads-stats">
                            <div class="stat-row">
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo number_format($analytics['custom_ads_views']); ?></h4>
                                    <p class="stat-label">Total Views</p>
                                </div>
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo number_format($analytics['custom_ads_clicks']); ?></h4>
                                    <p class="stat-label">Total Clicks</p>
                                </div>
                                <div class="stat-item">
                                    <h4 class="stat-value"><?php echo $analytics['custom_ads_ctr']; ?>%</h4>
                                    <p class="stat-label">CTR</p>
                                </div>
                            </div>
                            
                            <?php if ($analytics['custom_ads_views'] == 0): ?>
                            <div class="empty-state">
                                <i class="ri-advertisement-line empty-state-icon"></i>
                                <h3>No Custom Ads Data</h3>
                                <p>Create custom ads to see performance metrics here.</p>
                                <a href="custom-ad-add.php" class="btn btn-primary">
                                    <i class="ri-add-line"></i>
                                    Create Custom Ad
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Key Metrics -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Key Metrics</h3>
                    </div>
                    <div class="card-body">
                        <div class="metrics-list">
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-money-dollar-circle-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">eCPM</h4>
                                    <p class="metric-value">$<?php echo $analytics['ecpm']; ?></p>
                                    <small class="metric-description">Effective cost per mille</small>
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-calendar-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">Daily Average</h4>
                                    <p class="metric-value">$<?php echo number_format($analytics['total_revenue'] / max(1, count($analytics['daily_stats'])), 2); ?></p>
                                    <small class="metric-description">Average daily revenue</small>
                                </div>
                            </div>
                            
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="ri-line-chart-line"></i>
                                </div>
                                <div class="metric-content">
                                    <h4 class="metric-title">Fill Rate</h4>
                                    <p class="metric-value">98.5%</p>
                                    <small class="metric-description">Ad request fill rate</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($analytics['daily_stats'], 'date')); ?>,
        datasets: [{
            label: 'Revenue ($)',
            data: <?php echo json_encode(array_column($analytics['daily_stats'], 'revenue')); ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value;
                    }
                }
            }
        }
    }
});
</script>

<style>
.date-filter-form .input-group {
    gap: 0.5rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

.ad-type-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ad-type-item {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.ad-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.ad-type-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0;
}

.ad-type-revenue {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--success-600);
}

.ad-type-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
}

.metric-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.custom-ads-stats .stat-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.metrics-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

.metric-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-100);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.5rem;
}

.metric-content {
    flex: 1;
}

.metric-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.metric-content .metric-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.metric-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}
</style>

<?php include 'includes/footer.php'; ?>
