<?php
/**
 * 5G Smart VPN Admin Panel - Notification Functions
 * Firebase FCM Integration with Enhanced Features
 */

require_once __DIR__ . '/../../includes/config.php';

/**
 * Send FCM Notification with Firebase Cloud Messaging
 */
function sendFCMNotification($title, $message, $topic = 'all', $scheduleType = 'immediate', $scheduledTime = null, $recurringInterval = null, $data = []) {
    global $conn;

    try {
        // For scheduled notifications, save to database with pending status
        if ($scheduleType !== 'immediate') {
            $nextRunTime = $scheduledTime;

            // Check which columns exist in the table
            $columns_check = mysqli_query($conn, "SHOW COLUMNS FROM notifications");
            $existing_columns = [];
            while ($col = mysqli_fetch_assoc($columns_check)) {
                $existing_columns[] = $col['Field'];
            }

            // Build SQL query based on existing columns
            $base_columns = ['title', 'message', 'status', 'sent_to'];
            $base_values = ['?', '?', '?', '?'];
            $bind_types = 'ssss';
            $bind_values = [$title, $message, 'scheduled', $topic];

            // Add optional columns if they exist
            if (in_array('notification_type', $existing_columns)) {
                $base_columns[] = 'notification_type';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = 'scheduled';
            }
            if (in_array('schedule_type', $existing_columns)) {
                $base_columns[] = 'schedule_type';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = $scheduleType;
            }
            if (in_array('scheduled_time', $existing_columns) && $scheduledTime) {
                $base_columns[] = 'scheduled_time';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = $scheduledTime;
            }
            if (in_array('recurring_interval', $existing_columns) && $recurringInterval) {
                $base_columns[] = 'recurring_interval';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = $recurringInterval;
            }
            if (in_array('next_run_time', $existing_columns) && $nextRunTime) {
                $base_columns[] = 'next_run_time';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = $nextRunTime;
            }
            if (in_array('data', $existing_columns)) {
                $base_columns[] = 'data';
                $base_values[] = '?';
                $bind_types .= 's';
                $bind_values[] = json_encode($data);
            }

            $sql = "INSERT INTO notifications (" . implode(', ', $base_columns) . ") VALUES (" . implode(', ', $base_values) . ")";

            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param($bind_types, ...$bind_values);
                if ($stmt->execute()) {
                    return [
                        'success' => true,
                        'message' => 'Notification scheduled successfully',
                        'notification_id' => $conn->insert_id
                    ];
                } else {
                    throw new Exception('Failed to execute scheduled notification query: ' . $stmt->error);
                }
            } else {
                throw new Exception('Failed to prepare scheduled notification query: ' . $conn->error);
            }
        }

        $serviceAccountKeyFile = __DIR__ . '/../service-account-file.json';

        if (!file_exists($serviceAccountKeyFile)) {
            throw new Exception('Firebase service account file not found');
        }

        // Get the access token
        $accessToken = getAccessToken($serviceAccountKeyFile);

        if (!$accessToken) {
            throw new Exception('Failed to get Firebase access token');
        }

        // Prepare the FCM message
        $fcmMessage = [
            "message" => [
                "topic" => $topic,
                "notification" => [
                    "title" => $title,
                    "body" => $message
                ],
                "android" => [
                    "notification" => [
                        "icon" => "ic_notification",
                        "color" => "#3b82f6",
                        "sound" => "default",
                        "click_action" => "FLUTTER_NOTIFICATION_CLICK"
                    ]
                ],
                "apns" => [
                    "payload" => [
                        "aps" => [
                            "alert" => [
                                "title" => $title,
                                "body" => $message
                            ],
                            "sound" => "default"
                        ]
                    ]
                ]
            ]
        ];

        // Add custom data if provided (ensure all values are strings)
        if (!empty($data)) {
            $stringData = [];
            foreach ($data as $key => $value) {
                // Convert all values to strings as required by FCM
                if (is_bool($value)) {
                    $stringData[$key] = $value ? 'true' : 'false';
                } elseif (is_array($value) || is_object($value)) {
                    $stringData[$key] = json_encode($value);
                } else {
                    $stringData[$key] = (string)$value;
                }
            }
            $fcmMessage["message"]["data"] = $stringData;
        }

        // Send to FCM
        $url = 'https://fcm.googleapis.com/v1/projects/smartvpn-97d16/messages:send';
        $headers = [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmMessage));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $responseData = json_decode($response, true);

        // Save notification to database
        $sql = "INSERT INTO notifications (
            title, message, status, response, sent_to,
            notification_type, schedule_type, data
        ) VALUES (?, ?, ?, ?, ?, 'general', ?, ?)";

        $stmt = $conn->prepare($sql);
        $responseJson = json_encode($responseData);
        $status = ($httpcode == 200) ? 'sent' : 'error';
        $dataJson = json_encode($data);

        $stmt->bind_param("sssssss",
            $title,
            $message,
            $status,
            $responseJson,
            $topic,
            $scheduleType,
            $dataJson
        );
        $stmt->execute();

        return [
            'success' => ($httpcode == 200),
            'response' => $responseData,
            'status' => $status,
            'notification_id' => $conn->insert_id
        ];

    } catch (Exception $e) {
        // Log error and save failed notification
        $sql = "INSERT INTO notifications (
            title, message, status, response, sent_to,
            notification_type, schedule_type, data
        ) VALUES (?, ?, 'error', ?, ?, 'general', ?, ?)";

        $stmt = $conn->prepare($sql);
        $errorResponse = json_encode(['error' => $e->getMessage()]);
        $dataJson = json_encode($data);
        $stmt->bind_param("sssssss",
            $title,
            $message,
            $errorResponse,
            $topic,
            $scheduleType,
            $dataJson
        );
        $stmt->execute();

        return [
            'success' => false,
            'error' => $e->getMessage(),
            'notification_id' => $conn->insert_id
        ];
    }
}

/**
 * Get Firebase Access Token using Service Account
 */
function getAccessToken($serviceAccountKeyFile) {
    $serviceAccountKey = json_decode(file_get_contents($serviceAccountKeyFile), true);

    $now = time();
    $payload = [
        'iss' => $serviceAccountKey['client_email'],
        'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
        'aud' => 'https://oauth2.googleapis.com/token',
        'iat' => $now,
        'exp' => $now + 3600
    ];

    $jwt = createJWT($payload, $serviceAccountKey['private_key']);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt
    ]));

    $response = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($response, true);
    return $data['access_token'] ?? null;
}

/**
 * Create JWT Token for Firebase Authentication
 */
function createJWT($payload, $privateKey) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
    $payload = json_encode($payload);

    $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

    $signature = '';
    openssl_sign($base64UrlHeader . '.' . $base64UrlPayload, $signature, $privateKey, OPENSSL_ALGO_SHA256);

    $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    return $base64UrlHeader . '.' . $base64UrlPayload . '.' . $base64UrlSignature;
}

/**
 * Get Recent Notifications
 */
function getRecentNotifications($limit = 10) {
    global $conn;
    $sql = "SELECT * FROM notifications ORDER BY created_at DESC LIMIT ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get Notification by ID
 */
function getNotificationById($id) {
    global $conn;
    $sql = "SELECT * FROM notifications WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

/**
 * Delete Notification
 */
function deleteNotification($id) {
    global $conn;
    $sql = "DELETE FROM notifications WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

/**
 * Get Notification Statistics
 */
function getNotificationStats() {
    global $conn;

    $today = date('Y-m-d');
    $stats_query = "SELECT
        COUNT(*) as total_notifications,
        SUM(CASE WHEN DATE(created_at) = '$today' THEN 1 ELSE 0 END) as sent_today,
        SUM(CASE WHEN status = 'pending' OR status = 'scheduled' THEN 1 ELSE 0 END) as pending_notifications,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as total_sent,
        SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_notifications
        FROM notifications";

    $result = mysqli_query($conn, $stats_query);
    if ($result) {
        return mysqli_fetch_assoc($result);
    }

    return [
        'total_notifications' => 0,
        'sent_today' => 0,
        'pending_notifications' => 0,
        'total_sent' => 0,
        'failed_notifications' => 0
    ];
}

/**
 * Send Bulk Notifications
 */
function sendBulkNotification($title, $message, $topics = ['all'], $data = []) {
    $results = [];

    foreach ($topics as $topic) {
        $result = sendFCMNotification($title, $message, $topic, 'immediate', null, null, $data);
        $results[$topic] = $result;
    }

    return $results;
}
