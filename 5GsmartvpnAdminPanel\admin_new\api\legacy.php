<?php
/**
 * 5G Smart VPN Admin Panel - Legacy Compatibility API
 * Provides backward compatibility with old API format while using modern settings
 */

require_once '../db.php';

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Missing parameters"]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Timestamp expired"]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Invalid signature"]);
    exit;
}

// Get pro servers api (legacy format)
if (isset($_GET['pkg'])) {

    try {
        $db = getDB();

        // Get settings from the modern settings table
        $settings_result = $db->fetchAll("SELECT setting_key, setting_value FROM settings WHERE
            setting_key LIKE 'admob_%' OR
            setting_key LIKE 'facebook_%' OR
            setting_key LIKE '%_type' OR
            setting_key IN ('banner_enabled', 'interstitial_enabled', 'rewarded_enabled', 'native_enabled', 'openad_enabled', 'test_mode', 'click_limit', 'show_frequency', 'reward_time')");

        $settings = [];
        foreach ($settings_result as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        // Get servers in legacy format
        $servers_list = $db->fetchAll("SELECT * FROM servers WHERE status = 1 ORDER BY pos DESC");

        // Build legacy response format (mimics old admob table structure)
        $data = array();
        $r = array();

        // Legacy field mapping (array indices as in old API)
        $r['app_id'] = $settings['admob_app_id'] ?? '';           // Index 1
        $r['banner'] = $settings['admob_banner_id'] ?? '';        // Index 2
        $r['interstitial'] = $settings['admob_interstitial_id'] ?? ''; // Index 3
        $r['native'] = $settings['admob_native_id'] ?? '';        // Index 4
        $r['rewarded'] = $settings['admob_rewarded_id'] ?? '';    // Index 5
        $r['reward_time'] = (int)($settings['reward_time'] ?? 30); // Index 6
        $r['openad'] = $settings['admob_openad_id'] ?? '';        // Index 7

        // Calculate active status
        $r['active'] = (
            $settings['banner_enabled'] == '1' ||
            $settings['interstitial_enabled'] == '1' ||
            $settings['rewarded_enabled'] == '1' ||
            $settings['native_enabled'] == '1' ||
            $settings['openad_enabled'] == '1'
        ) ? 1 : 0; // Index 8

        $r['servers'] = $servers_list;

        array_push($data, $r);

        echo json_encode($data);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            "error" => "Internal server error",
            "message" => "Failed to fetch configuration"
        ]);
    }
}
?>
