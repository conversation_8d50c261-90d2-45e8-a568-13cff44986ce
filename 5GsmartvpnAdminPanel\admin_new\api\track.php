<?php
/**
 * Ad Tracking API Endpoint
 * Receives tracking data from the Android app
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../db.php';
require_once '../config.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get request data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Also check for GET parameters (fallback)
if (!$data) {
    $data = $_GET;
}

// Validate required fields
$required_fields = ['ad_type', 'event_type'];
foreach ($required_fields as $field) {
    if (!isset($data[$field]) || empty($data[$field])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => "Missing required field: $field"
        ]);
        exit;
    }
}

// Verify API authentication
$timestamp = $data['timestamp'] ?? '';
$signature = $data['signature'] ?? '';

if (!empty($timestamp) && !empty($signature)) {
    $expected_signature = hash_hmac('sha256', $timestamp, $API_KEY);
    if (!hash_equals($expected_signature, $signature)) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid signature'
        ]);
        exit;
    }
    
    // Check timestamp (allow 5 minute window)
    $current_time = time();
    if (abs($current_time - $timestamp) > 300) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Request timestamp expired'
        ]);
        exit;
    }
}

try {
    $db = getDB();
    
    // Create ad_tracking table if it doesn't exist
    $create_table_sql = "
        CREATE TABLE IF NOT EXISTS ad_tracking (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_type VARCHAR(50) NOT NULL,
            ad_id INT DEFAULT NULL,
            event_type VARCHAR(20) NOT NULL,
            user_id VARCHAR(100) DEFAULT NULL,
            device_info TEXT DEFAULT NULL,
            app_version VARCHAR(20) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ad_type (ad_type),
            INDEX idx_event_type (event_type),
            INDEX idx_created_at (created_at),
            INDEX idx_ad_id (ad_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($create_table_sql);
    
    // Prepare tracking data
    $tracking_data = [
        'ad_type' => $data['ad_type'],
        'ad_id' => $data['ad_id'] ?? null,
        'event_type' => $data['event_type'],
        'user_id' => $data['user_id'] ?? null,
        'device_info' => isset($data['device_info']) ? json_encode($data['device_info']) : null,
        'app_version' => $data['app_version'] ?? null
    ];
    
    // Insert tracking record
    $insert_sql = "
        INSERT INTO ad_tracking (ad_type, ad_id, event_type, user_id, device_info, app_version)
        VALUES (:ad_type, :ad_id, :event_type, :user_id, :device_info, :app_version)
    ";
    
    $stmt = $db->prepare($insert_sql);
    $stmt->execute($tracking_data);
    
    $tracking_id = $db->lastInsertId();
    
    // Update custom_ads table if this is a custom ad event
    if ($data['ad_type'] === 'custom' && !empty($data['ad_id'])) {
        $ad_id = (int)$data['ad_id'];
        
        if ($data['event_type'] === 'view') {
            $update_sql = "UPDATE custom_ads SET view_count = view_count + 1 WHERE id = :ad_id";
        } elseif ($data['event_type'] === 'click') {
            $update_sql = "UPDATE custom_ads SET click_count = click_count + 1 WHERE id = :ad_id";
        }
        
        if (isset($update_sql)) {
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute(['ad_id' => $ad_id]);
        }
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'tracking_id' => $tracking_id,
        'message' => 'Tracking data recorded successfully',
        'data' => [
            'ad_type' => $tracking_data['ad_type'],
            'event_type' => $tracking_data['event_type'],
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
