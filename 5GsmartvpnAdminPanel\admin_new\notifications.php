<?php
/**
 * 5G Smart VPN Admin Panel - Enhanced Notifications Management
 * Firebase FCM Integration with Scheduling and Templates
 */

session_start();
require_once 'includes/config.php';
require_once 'notifications/includes/notification_functions.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Notifications Management';

// Initialize database tables if needed
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'notifications'");
if (mysqli_num_rows($table_check) == 0) {
    include 'notifications/includes/create_notifications_table.php';
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'send_notification':
            $title = trim($_POST['title']);
            $message = trim($_POST['message']);
            $type = $_POST['type'] ?? 'general';
            $priority = $_POST['priority'] ?? 'normal';
            $scheduleType = $_POST['schedule_type'] ?? 'immediate';
            $scheduledTime = $_POST['scheduled_time'] ?? null;
            $recurringInterval = $_POST['recurring_interval'] ?? null;
            $topic = $_POST['topic'] ?? 'all';

            if (empty($title) || empty($message)) {
                echo json_encode(['success' => false, 'message' => 'Title and message are required']);
                exit();
            }

            // Prepare additional data
            $data = [
                'priority' => $priority,
                'category' => $type,
                'admin_id' => $_SESSION['admin_id'] ?? 0
            ];

            // Send notification using FCM
            $result = sendFCMNotification(
                $title,
                $message,
                $topic,
                $scheduleType,
                $scheduledTime,
                $recurringInterval,
                $data
            );

            if ($result['success']) {
                $message_text = $scheduleType === 'immediate' ? 'Notification sent successfully' : 'Notification scheduled successfully';
                echo json_encode(['success' => true, 'message' => $message_text, 'notification_id' => $result['notification_id'] ?? null]);
            } else {
                echo json_encode(['success' => false, 'message' => $result['error'] ?? 'Failed to send notification']);
            }
            exit();

        case 'delete_notification':
            $notification_id = (int)$_POST['notification_id'];

            if (deleteNotification($notification_id)) {
                echo json_encode(['success' => true, 'message' => 'Notification deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to delete notification']);
            }
            exit();

        case 'resend_notification':
            try {
                $notification_id = (int)$_POST['notification_id'];

                // Check if notification functions are available
                if (!function_exists('getNotificationById')) {
                    echo json_encode(['success' => false, 'message' => 'Notification functions not available']);
                    exit();
                }

                $notification = getNotificationById($notification_id);

                if (!$notification) {
                    echo json_encode(['success' => false, 'message' => 'Notification not found']);
                    exit();
                }

                // Prepare data for resending
                $data = [];
                if (isset($notification['data']) && !empty($notification['data'])) {
                    $data = json_decode($notification['data'], true) ?? [];
                }

                // Add resend metadata
                $data['resent'] = true;
                $data['resent_at'] = date('Y-m-d H:i:s');
                $data['original_id'] = $notification_id;

                $result = sendFCMNotification(
                    $notification['title'],
                    $notification['message'],
                    $notification['sent_to'] ?? 'all',
                    'immediate',
                    null,
                    null,
                    $data
                );

                if ($result['success']) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Notification resent successfully',
                        'new_notification_id' => $result['notification_id'] ?? null
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => $result['error'] ?? 'Failed to resend notification',
                        'debug_info' => $result
                    ]);
                }
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
            exit();

        case 'update_notification':
            try {
                $notification_id = (int)$_POST['notification_id'];
                $title = trim($_POST['title']);
                $message = trim($_POST['message']);
                $type = $_POST['type'] ?? 'general';
                $priority = $_POST['priority'] ?? 'normal';
                $scheduleType = $_POST['schedule_type'] ?? 'immediate';
                $scheduledTime = $_POST['scheduled_time'] ?? null;
                $recurringInterval = $_POST['recurring_interval'] ?? null;
                $topic = $_POST['topic'] ?? 'all';

                if (empty($title) || empty($message)) {
                    echo json_encode(['success' => false, 'message' => 'Title and message are required']);
                    exit();
                }

                // Build update query based on schedule type
                if ($scheduleType === 'immediate') {
                    // Update and mark for immediate sending
                    $sql = "UPDATE notifications SET
                            title = ?, message = ?, category = ?, priority = ?,
                            sent_to = ?, schedule_type = 'immediate',
                            scheduled_time = NULL, recurring_interval = NULL,
                            next_run_time = NULL, status = 'pending',
                            updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssi", $title, $message, $type, $priority, $topic, $notification_id);
                } else {
                    // Update scheduled notification
                    $nextRunTime = $scheduledTime;
                    $sql = "UPDATE notifications SET
                            title = ?, message = ?, category = ?, priority = ?,
                            sent_to = ?, schedule_type = ?, scheduled_time = ?,
                            recurring_interval = ?, next_run_time = ?, status = 'scheduled',
                            updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssssssi",
                        $title, $message, $type, $priority, $topic,
                        $scheduleType, $scheduledTime, $recurringInterval, $nextRunTime, $notification_id
                    );
                }

                if ($stmt->execute()) {
                    echo json_encode(['success' => true, 'message' => 'Notification updated successfully']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to update notification']);
                }

            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            exit();
    }
}

// Handle GET requests for fetching notification details
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'get_notification':
            $notification_id = (int)$_GET['id'];

            $sql = "SELECT * FROM notifications WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $notification_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($notification = $result->fetch_assoc()) {
                echo json_encode(['success' => true, 'notification' => $notification]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Notification not found']);
            }
            exit();
    }
}

// Get notifications with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Check if notifications table exists with proper structure
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'notifications'");
if (mysqli_num_rows($table_check) == 0) {
    // Create the enhanced notifications table
    include 'notifications/includes/create_notifications_table.php';
} else {
    // Check if table has the new structure, if not, add missing columns
    $columns_check = mysqli_query($conn, "SHOW COLUMNS FROM notifications LIKE 'sent_to'");
    if (mysqli_num_rows($columns_check) == 0) {
        // Add missing columns for compatibility
        $alter_queries = [
            "ALTER TABLE notifications ADD COLUMN sent_to VARCHAR(100) DEFAULT 'all' AFTER status",
            "ALTER TABLE notifications ADD COLUMN notification_type VARCHAR(50) DEFAULT 'general' AFTER sent_to",
            "ALTER TABLE notifications ADD COLUMN schedule_type VARCHAR(20) DEFAULT 'immediate' AFTER notification_type",
            "ALTER TABLE notifications ADD COLUMN scheduled_time DATETIME DEFAULT NULL AFTER schedule_type",
            "ALTER TABLE notifications ADD COLUMN recurring_interval VARCHAR(20) DEFAULT NULL AFTER scheduled_time",
            "ALTER TABLE notifications ADD COLUMN next_run_time DATETIME DEFAULT NULL AFTER recurring_interval",
            "ALTER TABLE notifications ADD COLUMN last_run_time DATETIME DEFAULT NULL AFTER next_run_time",
            "ALTER TABLE notifications ADD COLUMN data JSON DEFAULT NULL AFTER last_run_time",
            "ALTER TABLE notifications ADD COLUMN priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' AFTER data",
            "ALTER TABLE notifications ADD COLUMN category VARCHAR(50) DEFAULT 'general' AFTER priority",
            "ALTER TABLE notifications ADD COLUMN target_audience VARCHAR(100) DEFAULT 'all_users' AFTER category",
            "ALTER TABLE notifications ADD COLUMN delivery_count INT DEFAULT 0 AFTER target_audience",
            "ALTER TABLE notifications ADD COLUMN success_count INT DEFAULT 0 AFTER delivery_count",
            "ALTER TABLE notifications ADD COLUMN failure_count INT DEFAULT 0 AFTER success_count",
            "ALTER TABLE notifications ADD COLUMN response TEXT AFTER failure_count",
            "ALTER TABLE notifications ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
        ];

        foreach ($alter_queries as $query) {
            mysqli_query($conn, $query);
        }
    }
}

// Count total notifications
$count_query = "SELECT COUNT(*) as total FROM notifications";
$count_result = mysqli_query($conn, $count_query);
$total_notifications = $count_result ? mysqli_fetch_assoc($count_result)['total'] : 0;
$total_pages = ceil($total_notifications / $limit);

// Get notifications
$query = "SELECT * FROM notifications ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$result = mysqli_query($conn, $query);
$notifications = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $notifications[] = $row;
    }
}

// Get enhanced stats
$stats = getNotificationStats();
$stats['total_notifications'] = $total_notifications;

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Notifications Management</h1>
                <p class="page-subtitle">Send push notifications to your users</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="showSendModal()">
                        <i class="ri-send-plane-line"></i>
                        <span class="hide-mobile">Send Notification</span>
                    </button>
                    <button class="btn btn-secondary" onclick="refreshPage()">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Refresh</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-notification-4-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_notifications']; ?></h3>
                        <p class="stat-label">Total Notifications</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            <?php echo $stats['sent_today']; ?> Today
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-send-plane-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_sent']; ?></h3>
                        <p class="stat-label">Sent Notifications</p>
                        <span class="stat-change positive">
                            <i class="ri-check-line"></i>
                            Delivered
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-time-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['pending_notifications']; ?></h3>
                        <p class="stat-label">Pending</p>
                        <span class="stat-change neutral">
                            <i class="ri-clock-line"></i>
                            Scheduled
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-calendar-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['sent_today']; ?></h3>
                        <p class="stat-label">Today's Sends</p>
                        <span class="stat-change positive">
                            <i class="ri-calendar-check-line"></i>
                            <?php echo date('M j'); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Notifications Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">All Notifications</h3>
                    <div class="card-actions">
                        <div class="search-box">
                            <input type="text" id="notificationSearch" placeholder="Search notifications..." class="form-control">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table" id="notificationsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Notification</th>
                                    <th>Type</th>
                                    <th>Schedule</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($notifications as $notification): ?>
                                <tr data-notification-id="<?php echo $notification['id']; ?>">
                                    <td><?php echo $notification['id']; ?></td>
                                    <td>
                                        <div>
                                            <h4 class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></h4>
                                            <p class="text-muted" style="font-size: 0.875rem; margin: 0;">
                                                <?php echo htmlspecialchars(substr($notification['message'], 0, 100)) . (strlen($notification['message']) > 100 ? '...' : ''); ?>
                                            </p>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge-modern badge-<?php echo $notification['type'] === 'urgent' ? 'danger' : ($notification['type'] === 'update' ? 'info' : 'success'); ?>">
                                            <?php echo ucfirst($notification['type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $scheduleType = $notification['schedule_type'] ?? 'immediate';
                                        $scheduledTime = $notification['scheduled_time'] ?? null;
                                        $recurringInterval = $notification['recurring_interval'] ?? null;
                                        ?>
                                        <div style="font-size: 0.875rem;">
                                            <?php if ($scheduleType === 'immediate'): ?>
                                                <span class="badge-modern" style="background: #e3f2fd; color: #1976d2;">
                                                    <i class="ri-send-plane-line"></i> Immediate
                                                </span>
                                            <?php elseif ($scheduleType === 'scheduled'): ?>
                                                <span class="badge-modern" style="background: #fff3e0; color: #f57c00;">
                                                    <i class="ri-calendar-line"></i> Scheduled
                                                </span>
                                                <?php if ($scheduledTime): ?>
                                                    <div class="text-muted" style="font-size: 0.75rem; margin-top: 2px;">
                                                        <?php echo date('M j, Y g:i A', strtotime($scheduledTime)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php elseif ($scheduleType === 'recurring'): ?>
                                                <span class="badge-modern" style="background: #f3e5f5; color: #7b1fa2;">
                                                    <i class="ri-repeat-line"></i> Recurring
                                                </span>
                                                <?php if ($recurringInterval): ?>
                                                    <div class="text-muted" style="font-size: 0.75rem; margin-top: 2px;">
                                                        <?php echo ucfirst($recurringInterval); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($scheduledTime): ?>
                                                    <div class="text-muted" style="font-size: 0.75rem;">
                                                        Next: <?php echo date('M j, g:i A', strtotime($scheduledTime)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $notification['status']; ?>">
                                            <?php echo ucfirst($notification['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="font-size: 0.875rem;">
                                            <div><?php echo date('M j, Y', strtotime($notification['created_at'])); ?></div>
                                            <div class="text-muted"><?php echo date('g:i A', strtotime($notification['created_at'])); ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-secondary"
                                                    onclick="viewNotification(<?php echo $notification['id']; ?>)"
                                                    title="View Details">
                                                <i class="ri-eye-line"></i>
                                            </button>
                                            <?php if ($notification['status'] === 'scheduled' || $notification['schedule_type'] !== 'immediate'): ?>
                                            <button class="btn btn-sm btn-info"
                                                    onclick="editNotification(<?php echo $notification['id']; ?>)"
                                                    title="Edit Scheduled Notification">
                                                <i class="ri-edit-line"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if ($notification['status'] === 'sent' || $notification['status'] === 'error'): ?>
                                            <button class="btn btn-sm btn-primary"
                                                    onclick="resendNotification(<?php echo $notification['id']; ?>)"
                                                    title="Resend Notification">
                                                <i class="ri-send-plane-line"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deleteNotification(<?php echo $notification['id']; ?>)"
                                                    title="Delete Notification">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>

                                <?php if (empty($notifications)): ?>
                                <tr>
                                    <td colspan="7" class="text-center" style="padding: 3rem;">
                                        <div style="color: #6b7280;">
                                            <i class="ri-notification-4-line" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                                            <h3>No Notifications Found</h3>
                                            <p>Send your first notification to get started.</p>
                                            <button class="btn btn-primary" onclick="showSendModal()" style="margin-top: 1rem;">
                                                <i class="ri-send-plane-line"></i> Send Notification
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?>"
                               class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<!-- Send Notification Modal -->
<div id="sendModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Send New Notification</h3>
            <button class="modal-close" onclick="closeSendModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="sendNotificationForm">
                <div class="form-group">
                    <label for="notificationTitle" class="form-label">Title *</label>
                    <input type="text" id="notificationTitle" name="title" class="form-control" placeholder="Enter notification title" required>
                </div>

                <div class="form-group">
                    <label for="notificationMessage" class="form-label">Message *</label>
                    <textarea id="notificationMessage" name="message" class="form-control" rows="4" placeholder="Enter notification message" required></textarea>
                </div>

                <div class="form-group">
                    <label for="notificationType" class="form-label">Category</label>
                    <select id="notificationType" name="type" class="form-control">
                        <option value="general">General</option>
                        <option value="update">Update</option>
                        <option value="urgent">Urgent</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="security">Security</option>
                        <option value="welcome">Welcome</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notificationPriority" class="form-label">Priority</label>
                    <select id="notificationPriority" name="priority" class="form-control">
                        <option value="low">Low</option>
                        <option value="normal" selected>Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="scheduleType" class="form-label">Schedule Type</label>
                    <select id="scheduleType" name="schedule_type" class="form-control" onchange="toggleScheduleOptions()">
                        <option value="immediate" selected>Send Immediately</option>
                        <option value="scheduled">Schedule for Later</option>
                        <option value="recurring">Recurring Notification</option>
                    </select>
                </div>

                <div id="scheduleOptions" style="display: none;">
                    <div class="form-group">
                        <label for="scheduledTime" class="form-label">Scheduled Time</label>
                        <input type="datetime-local" id="scheduledTime" name="scheduled_time" class="form-control">
                    </div>

                    <div id="recurringOptions" style="display: none;">
                        <div class="form-group">
                            <label for="recurringInterval" class="form-label">Repeat Interval</label>
                            <select id="recurringInterval" name="recurring_interval" class="form-control">
                                <option value="hourly">Every Hour</option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notificationTopic" class="form-label">Target Audience</label>
                    <select id="notificationTopic" name="topic" class="form-control">
                        <option value="all">All Users</option>
                        <option value="premium">Premium Users</option>
                        <option value="free">Free Users</option>
                        <option value="new_users">New Users</option>
                        <option value="active_users">Active Users</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeSendModal()">Cancel</button>
            <button class="btn btn-primary" onclick="sendNotification()">
                <i class="ri-send-plane-line"></i> Send Notification
            </button>
        </div>
    </div>
</div>

<!-- Edit Notification Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit Scheduled Notification</h3>
            <button class="modal-close" onclick="closeEditModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editNotificationForm">
                <input type="hidden" id="editNotificationId" name="notification_id">

                <div class="form-group">
                    <label for="editNotificationTitle" class="form-label">Title *</label>
                    <input type="text" id="editNotificationTitle" name="title" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="editNotificationMessage" class="form-label">Message *</label>
                    <textarea id="editNotificationMessage" name="message" class="form-control" rows="4" required></textarea>
                </div>

                <div class="form-group">
                    <label for="editNotificationType" class="form-label">Category</label>
                    <select id="editNotificationType" name="type" class="form-control">
                        <option value="general">General</option>
                        <option value="update">Update</option>
                        <option value="urgent">Urgent</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="security">Security</option>
                        <option value="welcome">Welcome</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="editNotificationPriority" class="form-label">Priority</label>
                    <select id="editNotificationPriority" name="priority" class="form-control">
                        <option value="low">Low</option>
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="editScheduleType" class="form-label">Schedule Type</label>
                    <select id="editScheduleType" name="schedule_type" class="form-control" onchange="toggleEditScheduleOptions()">
                        <option value="immediate">Send Immediately</option>
                        <option value="scheduled">Schedule for Later</option>
                        <option value="recurring">Recurring Notification</option>
                    </select>
                </div>

                <div id="editScheduleOptions" style="display: none;">
                    <div class="form-group">
                        <label for="editScheduledTime" class="form-label">Scheduled Time</label>
                        <input type="datetime-local" id="editScheduledTime" name="scheduled_time" class="form-control">
                    </div>

                    <div id="editRecurringOptions" style="display: none;">
                        <div class="form-group">
                            <label for="editRecurringInterval" class="form-label">Repeat Interval</label>
                            <select id="editRecurringInterval" name="recurring_interval" class="form-control">
                                <option value="hourly">Every Hour</option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="editNotificationTopic" class="form-label">Target Audience</label>
                    <select id="editNotificationTopic" name="topic" class="form-control">
                        <option value="all">All Users</option>
                        <option value="premium">Premium Users</option>
                        <option value="free">Free Users</option>
                        <option value="new_users">New Users</option>
                        <option value="active_users">Active Users</option>
                    </select>
                </div>

                <!-- Additional Info Display -->
                <div class="form-group">
                    <label class="form-label">Current Status</label>
                    <div id="editCurrentStatus" class="form-control-static"></div>
                </div>

                <div class="form-group" id="editCreatedAtGroup">
                    <label class="form-label">Created At</label>
                    <div id="editCreatedAt" class="form-control-static"></div>
                </div>

                <div class="form-group" id="editNextRunGroup" style="display: none;">
                    <label class="form-label">Next Run Time</label>
                    <div id="editNextRunTime" class="form-control-static"></div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
            <button class="btn btn-primary" onclick="updateNotification()">
                <i class="ri-save-line"></i> Update Notification
            </button>
        </div>
    </div>
</div>

<!-- View Notification Modal -->
<div id="viewModal" class="modal">
    <div class="modal-content view-modal-content">
        <div class="modal-header view-modal-header">
            <div class="view-modal-icon">
                <i class="ri-notification-4-line"></i>
            </div>
            <h3 id="viewModalTitle">Notification Details</h3>
            <button class="modal-close" onclick="closeViewModal()">&times;</button>
        </div>
        <div class="modal-body view-modal-body">
            <!-- Notification Title -->
            <div class="view-section">
                <div class="view-label">
                    <i class="ri-text"></i>
                    <span>Title</span>
                </div>
                <div class="view-content" id="viewNotificationTitle"></div>
            </div>

            <!-- Notification Message -->
            <div class="view-section">
                <div class="view-label">
                    <i class="ri-message-3-line"></i>
                    <span>Message</span>
                </div>
                <div class="view-content" id="viewNotificationMessage"></div>
            </div>

            <!-- Status and Type Row -->
            <div class="view-row">
                <div class="view-section">
                    <div class="view-label">
                        <i class="ri-checkbox-circle-line"></i>
                        <span>Status</span>
                    </div>
                    <div class="view-content">
                        <span id="viewNotificationStatus" class="status-badge"></span>
                    </div>
                </div>
                <div class="view-section">
                    <div class="view-label">
                        <i class="ri-price-tag-3-line"></i>
                        <span>Category</span>
                    </div>
                    <div class="view-content">
                        <span id="viewNotificationType" class="badge-modern"></span>
                    </div>
                </div>
            </div>

            <!-- Priority and Audience Row -->
            <div class="view-row">
                <div class="view-section">
                    <div class="view-label">
                        <i class="ri-flag-line"></i>
                        <span>Priority</span>
                    </div>
                    <div class="view-content">
                        <span id="viewNotificationPriority" class="priority-badge"></span>
                    </div>
                </div>
                <div class="view-section">
                    <div class="view-label">
                        <i class="ri-group-line"></i>
                        <span>Target Audience</span>
                    </div>
                    <div class="view-content" id="viewNotificationAudience"></div>
                </div>
            </div>

            <!-- Schedule Information -->
            <div id="viewScheduleSection" class="view-section">
                <div class="view-label">
                    <i class="ri-calendar-line"></i>
                    <span>Schedule Information</span>
                </div>
                <div class="view-content">
                    <div class="schedule-info">
                        <div class="schedule-type">
                            <span id="viewScheduleType" class="schedule-badge"></span>
                        </div>
                        <div id="viewScheduleDetails" class="schedule-details"></div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="view-row">
                <div class="view-section">
                    <div class="view-label">
                        <i class="ri-time-line"></i>
                        <span>Created At</span>
                    </div>
                    <div class="view-content" id="viewCreatedAt"></div>
                </div>
                <div class="view-section" id="viewUpdatedSection" style="display: none;">
                    <div class="view-label">
                        <i class="ri-refresh-line"></i>
                        <span>Updated At</span>
                    </div>
                    <div class="view-content" id="viewUpdatedAt"></div>
                </div>
            </div>

            <!-- Response Information (for sent notifications) -->
            <div id="viewResponseSection" class="view-section" style="display: none;">
                <div class="view-label">
                    <i class="ri-information-line"></i>
                    <span>Delivery Response</span>
                </div>
                <div class="view-content">
                    <div id="viewResponseContent" class="response-content"></div>
                </div>
            </div>
        </div>
        <div class="modal-footer view-modal-footer">
            <button class="btn btn-secondary" onclick="closeViewModal()">
                <i class="ri-close-line"></i> Close
            </button>
            <button id="viewEditButton" class="btn btn-primary" onclick="editFromView()" style="display: none;">
                <i class="ri-edit-line"></i> Edit
            </button>
            <button id="viewResendButton" class="btn btn-success" onclick="resendFromView()" style="display: none;">
                <i class="ri-send-plane-line"></i> Resend
            </button>
        </div>
    </div>
</div>

<style>
.badge-modern {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success { background: var(--success-50); color: var(--success-600); }
.badge-info { background: var(--info-50); color: var(--info-600); }
.badge-danger { background: var(--error-50); color: var(--error-600); }

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.sent { background: var(--success-50); color: var(--success-600); }
.status-badge.pending { background: var(--warning-50); color: var(--warning-600); }
.status-badge.scheduled { background: var(--info-50); color: var(--info-600); }

.form-control-static {
    padding: 0.5rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    color: #495057;
    font-size: 0.875rem;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

/* View Modal Styles */
.view-modal-content {
    max-width: 700px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.view-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-modal-icon {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-modal-icon i {
    font-size: 1.5rem;
}

.view-modal-header h3 {
    margin: 0;
    flex: 1;
    font-size: 1.25rem;
    font-weight: 600;
}

.view-modal-header .modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.view-modal-header .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.view-modal-body {
    padding: 1.5rem;
    background: #f8f9fa;
}

.view-section {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.view-row .view-section {
    margin-bottom: 0;
}

.view-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.view-label i {
    color: #6c757d;
    font-size: 1rem;
}

.view-content {
    color: #212529;
    line-height: 1.5;
}

.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.low {
    background: #d1ecf1;
    color: #0c5460;
}

.priority-badge.normal {
    background: #d4edda;
    color: #155724;
}

.priority-badge.high {
    background: #fff3cd;
    color: #856404;
}

.priority-badge.urgent {
    background: #f8d7da;
    color: #721c24;
}

.schedule-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.schedule-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    width: fit-content;
}

.schedule-badge.immediate {
    background: #e3f2fd;
    color: #1976d2;
}

.schedule-badge.scheduled {
    background: #fff3e0;
    color: #f57c00;
}

.schedule-badge.recurring {
    background: #f3e5f5;
    color: #7b1fa2;
}

.schedule-details {
    color: #6c757d;
    font-size: 0.875rem;
}

.response-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #495057;
    max-height: 200px;
    overflow-y: auto;
}

.view-modal-footer {
    background: white;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    border-radius: 0 0 12px 12px;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Responsive Design */
@media (max-width: 768px) {
    .view-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .view-row {
        grid-template-columns: 1fr;
    }

    .view-modal-header {
        padding: 1rem;
    }

    .view-modal-body {
        padding: 1rem;
    }

    .view-modal-footer {
        padding: 1rem;
        flex-direction: column;
    }
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}
</style>

<script>
// Show send modal
function showSendModal() {
    document.getElementById('sendModal').classList.add('show');
}

// Close send modal
function closeSendModal() {
    document.getElementById('sendModal').classList.remove('show');
    document.getElementById('sendNotificationForm').reset();
}

// Send notification
function sendNotification() {
    const form = document.getElementById('sendNotificationForm');
    const formData = new FormData(form);
    formData.append('action', 'send_notification');

    // Validate scheduled time if needed
    const scheduleType = formData.get('schedule_type');
    if (scheduleType !== 'immediate') {
        const scheduledTime = formData.get('scheduled_time');
        if (!scheduledTime) {
            showToast('Please select a scheduled time', 'error');
            return;
        }

        const scheduledDate = new Date(scheduledTime);
        const now = new Date();
        if (scheduledDate <= now) {
            showToast('Scheduled time must be in the future', 'error');
            return;
        }
    }

    // Show loading state
    const sendButton = document.querySelector('.modal-footer .btn-primary');
    const originalText = sendButton.innerHTML;
    sendButton.innerHTML = '<i class="ri-loader-4-line"></i> Sending...';
    sendButton.disabled = true;

    fetch('notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeSendModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('An error occurred', 'error');
    })
    .finally(() => {
        // Restore button state
        sendButton.innerHTML = originalText;
        sendButton.disabled = false;
    });
}

// Delete notification
function deleteNotification(notificationId) {
    if (confirm('Are you sure you want to delete this notification?')) {
        const formData = new FormData();
        formData.append('action', 'delete_notification');
        formData.append('notification_id', notificationId);

        fetch('notifications.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelector(`tr[data-notification-id="${notificationId}"]`).remove();
                showToast(data.message, 'success');
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('An error occurred', 'error');
        });
    }
}

// Global variable to store current notification ID for actions
let currentNotificationId = null;

// View notification details
function viewNotification(notificationId) {
    currentNotificationId = notificationId;

    // Fetch notification details from server
    fetch(`notifications.php?action=get_notification&id=${notificationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notification = data.notification;
                populateViewModal(notification);
                document.getElementById('viewModal').classList.add('show');
            } else {
                showToast('Failed to load notification details', 'error');
            }
        })
        .catch(error => {
            showToast('Error loading notification details', 'error');
        });
}

// Populate view modal with notification data
function populateViewModal(notification) {
    // Basic information
    document.getElementById('viewNotificationTitle').textContent = notification.title;
    document.getElementById('viewNotificationMessage').textContent = notification.message;

    // Status badge
    const statusBadge = document.getElementById('viewNotificationStatus');
    statusBadge.textContent = notification.status.charAt(0).toUpperCase() + notification.status.slice(1);
    statusBadge.className = `status-badge ${notification.status}`;

    // Type badge
    const typeBadge = document.getElementById('viewNotificationType');
    const type = notification.type || 'general';
    typeBadge.textContent = type.charAt(0).toUpperCase() + type.slice(1);
    typeBadge.className = `badge-modern badge-${type === 'urgent' ? 'danger' : (type === 'update' ? 'info' : 'success')}`;

    // Priority badge
    const priorityBadge = document.getElementById('viewNotificationPriority');
    const priority = notification.priority || 'normal';
    priorityBadge.textContent = priority.charAt(0).toUpperCase() + priority.slice(1);
    priorityBadge.className = `priority-badge ${priority}`;

    // Target audience
    const audience = notification.sent_to || 'all';
    document.getElementById('viewNotificationAudience').textContent =
        audience === 'all' ? 'All Users' :
        audience === 'premium' ? 'Premium Users' :
        audience === 'free' ? 'Free Users' :
        audience === 'new_users' ? 'New Users' :
        audience === 'active_users' ? 'Active Users' : audience;

    // Schedule information
    const scheduleType = notification.schedule_type || 'immediate';
    const scheduleBadge = document.getElementById('viewScheduleType');
    const scheduleDetails = document.getElementById('viewScheduleDetails');

    scheduleBadge.className = `schedule-badge ${scheduleType}`;

    if (scheduleType === 'immediate') {
        scheduleBadge.innerHTML = '<i class="ri-send-plane-line"></i> Immediate';
        scheduleDetails.textContent = 'Sent immediately upon creation';
    } else if (scheduleType === 'scheduled') {
        scheduleBadge.innerHTML = '<i class="ri-calendar-line"></i> Scheduled';
        if (notification.scheduled_time) {
            const scheduledDate = new Date(notification.scheduled_time);
            scheduleDetails.innerHTML = `
                <strong>Scheduled for:</strong> ${scheduledDate.toLocaleDateString()} at ${scheduledDate.toLocaleTimeString()}
            `;
        }
    } else if (scheduleType === 'recurring') {
        scheduleBadge.innerHTML = '<i class="ri-repeat-line"></i> Recurring';
        let details = '';
        if (notification.recurring_interval) {
            details += `<strong>Interval:</strong> ${notification.recurring_interval.charAt(0).toUpperCase() + notification.recurring_interval.slice(1)}<br>`;
        }
        if (notification.scheduled_time) {
            const scheduledDate = new Date(notification.scheduled_time);
            details += `<strong>First run:</strong> ${scheduledDate.toLocaleDateString()} at ${scheduledDate.toLocaleTimeString()}<br>`;
        }
        if (notification.next_run_time) {
            const nextDate = new Date(notification.next_run_time);
            details += `<strong>Next run:</strong> ${nextDate.toLocaleDateString()} at ${nextDate.toLocaleTimeString()}`;
        }
        scheduleDetails.innerHTML = details;
    }

    // Timestamps
    const createdDate = new Date(notification.created_at);
    document.getElementById('viewCreatedAt').textContent =
        `${createdDate.toLocaleDateString()} at ${createdDate.toLocaleTimeString()}`;

    // Updated timestamp (if exists)
    if (notification.updated_at && notification.updated_at !== notification.created_at) {
        const updatedDate = new Date(notification.updated_at);
        document.getElementById('viewUpdatedAt').textContent =
            `${updatedDate.toLocaleDateString()} at ${updatedDate.toLocaleTimeString()}`;
        document.getElementById('viewUpdatedSection').style.display = 'block';
    } else {
        document.getElementById('viewUpdatedSection').style.display = 'none';
    }

    // Response information (for sent notifications)
    if (notification.response && notification.status === 'sent') {
        try {
            const response = JSON.parse(notification.response);
            document.getElementById('viewResponseContent').textContent = JSON.stringify(response, null, 2);
            document.getElementById('viewResponseSection').style.display = 'block';
        } catch (e) {
            document.getElementById('viewResponseSection').style.display = 'none';
        }
    } else {
        document.getElementById('viewResponseSection').style.display = 'none';
    }

    // Action buttons
    const editButton = document.getElementById('viewEditButton');
    const resendButton = document.getElementById('viewResendButton');

    // Show edit button for scheduled notifications
    if (notification.status === 'scheduled' || notification.schedule_type !== 'immediate') {
        editButton.style.display = 'inline-flex';
    } else {
        editButton.style.display = 'none';
    }

    // Show resend button for sent/error notifications
    if (notification.status === 'sent' || notification.status === 'error') {
        resendButton.style.display = 'inline-flex';
    } else {
        resendButton.style.display = 'none';
    }
}

// Close view modal
function closeViewModal() {
    document.getElementById('viewModal').classList.remove('show');
    currentNotificationId = null;
}

// Edit from view modal
function editFromView() {
    if (currentNotificationId) {
        closeViewModal();
        editNotification(currentNotificationId);
    }
}

// Resend from view modal
function resendFromView() {
    if (currentNotificationId) {
        closeViewModal();
        resendNotification(currentNotificationId);
    }
}

// Edit notification
function editNotification(notificationId) {
    // Fetch notification details from server
    fetch(`notifications.php?action=get_notification&id=${notificationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notification = data.notification;

                // Populate edit form
                document.getElementById('editNotificationId').value = notification.id;
                document.getElementById('editNotificationTitle').value = notification.title;
                document.getElementById('editNotificationMessage').value = notification.message;
                document.getElementById('editNotificationType').value = notification.type || 'general';
                document.getElementById('editNotificationPriority').value = notification.priority || 'normal';
                document.getElementById('editScheduleType').value = notification.schedule_type || 'immediate';
                document.getElementById('editNotificationTopic').value = notification.sent_to || 'all';

                // Set scheduled time if exists
                if (notification.scheduled_time) {
                    const scheduledDate = new Date(notification.scheduled_time);
                    const localDateTime = new Date(scheduledDate.getTime() - scheduledDate.getTimezoneOffset() * 60000);
                    document.getElementById('editScheduledTime').value = localDateTime.toISOString().slice(0, 16);
                }

                // Set recurring interval if exists
                if (notification.recurring_interval) {
                    document.getElementById('editRecurringInterval').value = notification.recurring_interval;
                }

                // Update status display
                document.getElementById('editCurrentStatus').textContent = notification.status;
                document.getElementById('editCreatedAt').textContent = notification.created_at;

                // Show next run time if exists
                if (notification.next_run_time) {
                    document.getElementById('editNextRunTime').textContent = notification.next_run_time;
                    document.getElementById('editNextRunGroup').style.display = 'block';
                } else {
                    document.getElementById('editNextRunGroup').style.display = 'none';
                }

                // Toggle schedule options
                toggleEditScheduleOptions();

                // Show modal
                document.getElementById('editModal').classList.add('show');
            } else {
                showToast('Failed to load notification details', 'error');
            }
        })
        .catch(error => {
            showToast('Error loading notification details', 'error');
        });
}

// Close edit modal
function closeEditModal() {
    document.getElementById('editModal').classList.remove('show');
    document.getElementById('editNotificationForm').reset();
}

// Toggle edit schedule options
function toggleEditScheduleOptions() {
    const scheduleType = document.getElementById('editScheduleType').value;
    const scheduleOptions = document.getElementById('editScheduleOptions');
    const recurringOptions = document.getElementById('editRecurringOptions');

    if (scheduleType === 'immediate') {
        scheduleOptions.style.display = 'none';
        recurringOptions.style.display = 'none';
    } else if (scheduleType === 'scheduled') {
        scheduleOptions.style.display = 'block';
        recurringOptions.style.display = 'none';
        // Set minimum date to current time
        const now = new Date();
        now.setMinutes(now.getMinutes() + 1);
        document.getElementById('editScheduledTime').min = now.toISOString().slice(0, 16);
    } else if (scheduleType === 'recurring') {
        scheduleOptions.style.display = 'block';
        recurringOptions.style.display = 'block';
        // Set minimum date to current time
        const now = new Date();
        now.setMinutes(now.getMinutes() + 1);
        document.getElementById('editScheduledTime').min = now.toISOString().slice(0, 16);
    }
}

// Update notification
function updateNotification() {
    const form = document.getElementById('editNotificationForm');
    const formData = new FormData(form);
    formData.append('action', 'update_notification');

    // Validate scheduled time if needed
    const scheduleType = formData.get('schedule_type');
    if (scheduleType !== 'immediate') {
        const scheduledTime = formData.get('scheduled_time');
        if (!scheduledTime) {
            showToast('Please select a scheduled time', 'error');
            return;
        }

        const scheduledDate = new Date(scheduledTime);
        const now = new Date();
        if (scheduledDate <= now) {
            showToast('Scheduled time must be in the future', 'error');
            return;
        }
    }

    // Show loading state
    const updateButton = document.querySelector('.modal-footer .btn-primary');
    const originalText = updateButton.innerHTML;
    updateButton.innerHTML = '<i class="ri-loader-4-line"></i> Updating...';
    updateButton.disabled = true;

    fetch('notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeEditModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('An error occurred', 'error');
    })
    .finally(() => {
        // Restore button state
        updateButton.innerHTML = originalText;
        updateButton.disabled = false;
    });
}

// Search functionality
document.getElementById('notificationSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#notificationsTable tbody tr');

    rows.forEach(row => {
        const title = row.querySelector('.notification-title');
        if (title) {
            const titleText = title.textContent.toLowerCase();
            row.style.display = titleText.includes(searchTerm) ? '' : 'none';
        }
    });
});

// Refresh page
function refreshPage() {
    location.reload();
}

// Toast notification
function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10001;
        background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Resend notification
function resendNotification(notificationId) {
    if (confirm('Are you sure you want to resend this notification?')) {
        // Show loading state
        const resendButton = document.querySelector(`button[onclick="resendNotification(${notificationId})"]`);
        const originalHTML = resendButton.innerHTML;
        resendButton.innerHTML = '<i class="ri-loader-4-line"></i>';
        resendButton.disabled = true;

        const formData = new FormData();
        formData.append('action', 'resend_notification');
        formData.append('notification_id', notificationId);

        fetch('notifications.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Resend response:', data); // Debug log

            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                let errorMessage = data.message || 'Failed to resend notification';
                if (data.debug_info) {
                    console.error('Debug info:', data.debug_info);
                }
                if (data.file && data.line) {
                    console.error(`Error in ${data.file} at line ${data.line}`);
                }
                showToast(errorMessage, 'error');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            showToast('Network error: ' + error.message, 'error');
        })
        .finally(() => {
            // Restore button state
            resendButton.innerHTML = originalHTML;
            resendButton.disabled = false;
        });
    }
}

// Toggle schedule options
function toggleScheduleOptions() {
    const scheduleType = document.getElementById('scheduleType').value;
    const scheduleOptions = document.getElementById('scheduleOptions');
    const recurringOptions = document.getElementById('recurringOptions');

    if (scheduleType === 'immediate') {
        scheduleOptions.style.display = 'none';
        recurringOptions.style.display = 'none';
    } else if (scheduleType === 'scheduled') {
        scheduleOptions.style.display = 'block';
        recurringOptions.style.display = 'none';
        // Set minimum date to current time
        const now = new Date();
        now.setMinutes(now.getMinutes() + 1); // At least 1 minute in the future
        document.getElementById('scheduledTime').min = now.toISOString().slice(0, 16);
    } else if (scheduleType === 'recurring') {
        scheduleOptions.style.display = 'block';
        recurringOptions.style.display = 'block';
        // Set minimum date to current time
        const now = new Date();
        now.setMinutes(now.getMinutes() + 1);
        document.getElementById('scheduledTime').min = now.toISOString().slice(0, 16);
    }
}

// Initialize datetime input on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleScheduleOptions();
});

// Close modal when clicking outside
document.getElementById('sendModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSendModal();
    }
});

// Close edit modal when clicking outside
document.getElementById('editModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditModal();
    }
});

// Close view modal when clicking outside
document.getElementById('viewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeViewModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
