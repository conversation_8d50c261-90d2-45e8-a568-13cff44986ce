package com.official.fivegfastvpn.api;

import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.AppConfig;
import com.official.fivegfastvpn.model.CustomAd;
import com.official.fivegfastvpn.model.Server;
import org.json.JSONObject;
import java.util.List;

/**
 * API Test Activity for 5G Smart VPN
 * Use this activity to test API integration during development
 * Remove or disable in production builds
 */
public class ApiTestActivity extends AppCompatActivity {
    private static final String TAG = "ApiTestActivity";
    
    private TextView statusText;
    private VpnApiService apiService;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_api_test);
        
        statusText = findViewById(R.id.statusText);
        apiService = VpnApiService.getInstance(this);
        
        setupButtons();
        
        // Initial status check
        updateStatus("API Test Activity Loaded");
    }
    
    private void setupButtons() {
        Button testStatusBtn = findViewById(R.id.testStatusBtn);
        Button testConfigBtn = findViewById(R.id.testConfigBtn);
        Button testServersBtn = findViewById(R.id.testServersBtn);
        Button testCustomAdsBtn = findViewById(R.id.testCustomAdsBtn);
        Button testIpBtn = findViewById(R.id.testIpBtn);
        Button testTrackingBtn = findViewById(R.id.testTrackingBtn);
        
        testStatusBtn.setOnClickListener(v -> testApiStatus());
        testConfigBtn.setOnClickListener(v -> testAppConfig());
        testServersBtn.setOnClickListener(v -> testServers());
        testCustomAdsBtn.setOnClickListener(v -> testCustomAds());
        testIpBtn.setOnClickListener(v -> testIpDetection());
        testTrackingBtn.setOnClickListener(v -> testAdTracking());
    }
    
    private void testApiStatus() {
        updateStatus("Testing API Status...");
        
        apiService.checkApiStatus(new VpnApiService.ApiCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject status) {
                try {
                    String apiStatus = status.getString("api_status");
                    String database = status.optString("database", "unknown");
                    String apiVersion = status.optString("api_version", "unknown");
                    
                    String result = String.format(
                        "✅ API Status Test PASSED\n" +
                        "Status: %s\n" +
                        "Database: %s\n" +
                        "Version: %s\n" +
                        "Response: %s",
                        apiStatus, database, apiVersion, status.toString()
                    );
                    
                    updateStatus(result);
                    Log.d(TAG, "API Status: " + status.toString());
                    
                } catch (Exception e) {
                    updateStatus("❌ API Status Test FAILED: " + e.getMessage());
                    Log.e(TAG, "Error parsing status response", e);
                }
            }
            
            @Override
            public void onError(String error, int errorCode) {
                String result = String.format("❌ API Status Test FAILED\nError: %s\nCode: %d", error, errorCode);
                updateStatus(result);
                Log.e(TAG, "API Status Error: " + error);
            }
        });
    }
    
    private void testAppConfig() {
        updateStatus("Testing App Configuration...");
        
        apiService.getAppConfig(getPackageName(), new VpnApiService.ApiCallback<AppConfig>() {
            @Override
            public void onSuccess(AppConfig config) {
                String result = String.format(
                    "✅ App Config Test PASSED\n" +
                    "AdMob ID: %s\n" +
                    "Banner: %s\n" +
                    "Ads Status: %d\n" +
                    "Servers Count: %d\n" +
                    "API Version: %s",
                    config.getAdmobId(),
                    config.getAdmobBanner(),
                    config.getAdsStatus(),
                    config.getServers() != null ? config.getServers().size() : 0,
                    config.getApiVersion()
                );
                
                updateStatus(result);
                Log.d(TAG, "App Config loaded successfully");
            }
            
            @Override
            public void onError(String error, int errorCode) {
                String result = String.format("❌ App Config Test FAILED\nError: %s\nCode: %d", error, errorCode);
                updateStatus(result);
                Log.e(TAG, "App Config Error: " + error);
            }
        });
    }
    
    private void testServers() {
        updateStatus("Testing Servers API...");
        
        apiService.getServers(new VpnApiService.ApiCallback<List<Server>>() {
            @Override
            public void onSuccess(List<Server> servers) {
                StringBuilder result = new StringBuilder("✅ Servers Test PASSED\n");
                result.append(String.format("Total Servers: %d\n", servers.size()));
                
                for (int i = 0; i < Math.min(3, servers.size()); i++) {
                    Server server = servers.get(i);
                    result.append(String.format("Server %d: %s (Status: %d)\n", 
                        i + 1, server.getCountry(), server.getStatus()));
                }
                
                if (servers.size() > 3) {
                    result.append("... and ").append(servers.size() - 3).append(" more");
                }
                
                updateStatus(result.toString());
                Log.d(TAG, "Servers loaded: " + servers.size());
            }
            
            @Override
            public void onError(String error, int errorCode) {
                String result = String.format("❌ Servers Test FAILED\nError: %s\nCode: %d", error, errorCode);
                updateStatus(result);
                Log.e(TAG, "Servers Error: " + error);
            }
        });
    }
    
    private void testCustomAds() {
        updateStatus("Testing Custom Ads API...");
        
        apiService.getCustomAds(new VpnApiService.ApiCallback<CustomAd>() {
            @Override
            public void onSuccess(CustomAd ad) {
                String result = String.format(
                    "✅ Custom Ads Test PASSED\n" +
                    "Ad ID: %d\n" +
                    "Title: %s\n" +
                    "Text: %s\n" +
                    "URL: %s\n" +
                    "Views: %d, Clicks: %d\n" +
                    "Active: %s",
                    ad.getId(),
                    ad.getTitle(),
                    ad.getText(),
                    ad.getUrl(),
                    ad.getViewCount(),
                    ad.getClickCount(),
                    ad.isActive() ? "Yes" : "No"
                );
                
                updateStatus(result);
                Log.d(TAG, "Custom ad loaded: " + ad.getTitle());
            }
            
            @Override
            public void onError(String error, int errorCode) {
                String result = String.format("❌ Custom Ads Test FAILED\nError: %s\nCode: %d", error, errorCode);
                updateStatus(result);
                Log.e(TAG, "Custom Ads Error: " + error);
            }
        });
    }
    
    private void testIpDetection() {
        updateStatus("Testing IP Detection...");
        
        apiService.getClientIP(new VpnApiService.ApiCallback<String>() {
            @Override
            public void onSuccess(String ip) {
                String result = String.format("✅ IP Detection Test PASSED\nYour IP: %s", ip);
                updateStatus(result);
                Log.d(TAG, "Client IP: " + ip);
            }
            
            @Override
            public void onError(String error, int errorCode) {
                String result = String.format("❌ IP Detection Test FAILED\nError: %s\nCode: %d", error, errorCode);
                updateStatus(result);
                Log.e(TAG, "IP Detection Error: " + error);
            }
        });
    }
    
    private void testAdTracking() {
        updateStatus("Testing Ad Tracking...");
        
        // Test tracking a custom ad view
        apiService.trackAd(1, "view", "custom", null, "test_device", 
            new VpnApiService.ApiCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    String response = String.format("✅ Ad Tracking Test PASSED\nResult: %s", result);
                    updateStatus(response);
                    Log.d(TAG, "Ad tracking successful: " + result);
                }
                
                @Override
                public void onError(String error, int errorCode) {
                    String response = String.format("❌ Ad Tracking Test FAILED\nError: %s\nCode: %d", error, errorCode);
                    updateStatus(response);
                    Log.e(TAG, "Ad Tracking Error: " + error);
                }
            });
    }
    
    private void updateStatus(String message) {
        runOnUiThread(() -> {
            statusText.setText(message);
            Log.d(TAG, message);
        });
    }
    
    private void showToast(String message) {
        runOnUiThread(() -> Toast.makeText(this, message, Toast.LENGTH_SHORT).show());
    }
}
