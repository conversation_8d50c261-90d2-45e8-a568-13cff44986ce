<?php
/**
 * Debug Analytics Page - Simple version to test basic functionality
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

echo "<h1>Debug Analytics Page</h1>";
echo "<p>Testing basic functionality...</p>";

// Test 1: Database connection
echo "<h2>1. Database Connection Test</h2>";
if ($conn) {
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    echo "<p>Connection info: " . mysqli_get_host_info($conn) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

// Test 2: Check tables
echo "<h2>2. Table Check</h2>";
$tables = ['custom_ads', 'ad_tracking'];
foreach ($tables as $table) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        
        // Show row count
        $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
        $count_row = mysqli_fetch_assoc($count_result);
        echo "<p>   - Rows: " . $count_row['count'] . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Table '$table' does not exist</p>";
    }
}

// Test 3: API Key
echo "<h2>3. API Key Test</h2>";
if (isset($API_KEY)) {
    echo "<p style='color: green;'>✅ API Key is set: " . substr($API_KEY, 0, 10) . "...</p>";
} else {
    echo "<p style='color: red;'>❌ API Key is not set</p>";
}

// Test 4: Custom Ads Query
echo "<h2>4. Custom Ads Query Test</h2>";
try {
    $custom_query = "SELECT COUNT(*) as count, SUM(view_count) as views, SUM(click_count) as clicks FROM custom_ads WHERE `on` = 1";
    $result = mysqli_query($conn, $custom_query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p style='color: green;'>✅ Custom ads query successful</p>";
        echo "<p>   - Active ads: " . $row['count'] . "</p>";
        echo "<p>   - Total views: " . ($row['views'] ?? 0) . "</p>";
        echo "<p>   - Total clicks: " . ($row['clicks'] ?? 0) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Custom ads query failed: " . mysqli_error($conn) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Custom ads query exception: " . $e->getMessage() . "</p>";
}

// Test 5: Ad Tracking Query (if table exists)
echo "<h2>5. Ad Tracking Query Test</h2>";
$tracking_table_check = mysqli_query($conn, "SHOW TABLES LIKE 'ad_tracking'");
if (mysqli_num_rows($tracking_table_check) > 0) {
    try {
        $tracking_query = "SELECT COUNT(*) as count, ad_type, event_type FROM ad_tracking GROUP BY ad_type, event_type";
        $result = mysqli_query($conn, $tracking_query);
        if ($result) {
            echo "<p style='color: green;'>✅ Ad tracking query successful</p>";
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<p>   - " . $row['ad_type'] . " " . $row['event_type'] . ": " . $row['count'] . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Ad tracking query failed: " . mysqli_error($conn) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Ad tracking query exception: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ ad_tracking table doesn't exist yet - will be created when first tracking event occurs</p>";
}

// Test 6: PHP Version and Extensions
echo "<h2>6. PHP Environment Test</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQLi Extension: " . (extension_loaded('mysqli') ? '✅ Loaded' : '❌ Not loaded') . "</p>";
echo "<p>JSON Extension: " . (extension_loaded('json') ? '✅ Loaded' : '❌ Not loaded') . "</p>";
echo "<p>OpenSSL Extension: " . (extension_loaded('openssl') ? '✅ Loaded' : '❌ Not loaded') . "</p>";

// Test 7: File Permissions
echo "<h2>7. File Permissions Test</h2>";
$files_to_check = [
    'api/analytics.php',
    'api/track.php',
    'ad-analytics.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "<p>$file: " . substr(sprintf('%o', $perms), -4) . " " . (is_readable($file) ? '✅ Readable' : '❌ Not readable') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ $file does not exist</p>";
    }
}

// Test 8: Simple Analytics Calculation
echo "<h2>8. Simple Analytics Test</h2>";
$analytics = [
    'total_impressions' => 0,
    'total_clicks' => 0,
    'total_revenue' => 0,
    'ctr' => 0
];

// Get some basic data
try {
    $custom_result = mysqli_query($conn, "SELECT SUM(view_count) as views, SUM(click_count) as clicks FROM custom_ads");
    if ($custom_result) {
        $custom_data = mysqli_fetch_assoc($custom_result);
        $analytics['total_impressions'] = (int)($custom_data['views'] ?? 0);
        $analytics['total_clicks'] = (int)($custom_data['clicks'] ?? 0);
        
        if ($analytics['total_impressions'] > 0) {
            $analytics['ctr'] = round(($analytics['total_clicks'] / $analytics['total_impressions']) * 100, 2);
        }
        
        $analytics['total_revenue'] = $analytics['total_clicks'] * 0.05;
        
        echo "<p style='color: green;'>✅ Analytics calculation successful</p>";
        echo "<p>   - Impressions: " . number_format($analytics['total_impressions']) . "</p>";
        echo "<p>   - Clicks: " . number_format($analytics['total_clicks']) . "</p>";
        echo "<p>   - CTR: " . $analytics['ctr'] . "%</p>";
        echo "<p>   - Revenue: $" . number_format($analytics['total_revenue'], 2) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Analytics calculation failed: " . $e->getMessage() . "</p>";
}

echo "<h2>9. Next Steps</h2>";
echo "<p>If all tests above pass, try accessing the main analytics page:</p>";
echo "<p><a href='ad-analytics.php' target='_blank'>ad-analytics.php</a></p>";
echo "<p>If there are still errors, check the PHP error log for more details.</p>";

echo "<h2>10. Error Log Check</h2>";
$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    echo "<p>Error log location: $error_log_path</p>";
    $recent_errors = shell_exec("tail -10 '$error_log_path' 2>/dev/null");
    if ($recent_errors) {
        echo "<h3>Recent Errors:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px;'>" . htmlspecialchars($recent_errors) . "</pre>";
    }
} else {
    echo "<p>Error log not found or not accessible</p>";
}

echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
