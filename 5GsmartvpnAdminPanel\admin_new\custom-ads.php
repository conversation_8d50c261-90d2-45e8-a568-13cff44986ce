<?php
/**
 * 5G Smart VPN Admin Panel - Custom Ads Management
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Custom Ads Management';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'toggle_status':
            $ad_id = (int)$_POST['ad_id'];
            $current_status = (int)$_POST['current_status'];
            $new_status = $current_status ? 0 : 1;
            
            $query = "UPDATE custom_ads SET `on` = $new_status WHERE id = $ad_id";
            if (mysqli_query($conn, $query)) {
                echo json_encode(['success' => true, 'new_status' => $new_status]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update ad status']);
            }
            exit();
            
        case 'delete_ad':
            $ad_id = (int)$_POST['ad_id'];
            
            // Get ad details for file cleanup
            $ad_query = mysqli_query($conn, "SELECT image_url FROM custom_ads WHERE id = $ad_id");
            if ($ad_query && $ad_row = mysqli_fetch_assoc($ad_query)) {
                // Delete image file if exists
                if (!empty($ad_row['image_url']) && file_exists('../' . $ad_row['image_url'])) {
                    unlink('../' . $ad_row['image_url']);
                }
            }
            
            $query = "DELETE FROM custom_ads WHERE id = $ad_id";
            if (mysqli_query($conn, $query)) {
                echo json_encode(['success' => true, 'message' => 'Ad deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to delete ad']);
            }
            exit();
    }
}

// Get ads with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Count total ads
$count_query = "SELECT COUNT(*) as total FROM custom_ads";
$count_result = mysqli_query($conn, $count_query);
$total_ads = $count_result ? mysqli_fetch_assoc($count_result)['total'] : 0;
$total_pages = ceil($total_ads / $limit);

// Get ads
$query = "SELECT * FROM custom_ads ORDER BY id DESC LIMIT $limit OFFSET $offset";
$result = mysqli_query($conn, $query);
$ads = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $ads[] = $row;
    }
}

// Get stats
$stats = [
    'total_ads' => $total_ads,
    'active_ads' => 0,
    'expired_ads' => 0,
    'total_views' => 0,
    'total_clicks' => 0
];

if ($conn) {
    $current_date = date('Y-m-d');
    $stats_query = "SELECT 
        SUM(CASE WHEN `on` = 1 AND date_start <= '$current_date' AND date_end >= '$current_date' THEN 1 ELSE 0 END) as active_ads,
        SUM(CASE WHEN date_end < '$current_date' THEN 1 ELSE 0 END) as expired_ads,
        SUM(view_count) as total_views,
        SUM(click_count) as total_clicks
        FROM custom_ads";
    
    $stats_result = mysqli_query($conn, $stats_query);
    if ($stats_result) {
        $stats_data = mysqli_fetch_assoc($stats_result);
        $stats['active_ads'] = $stats_data['active_ads'] ?? 0;
        $stats['expired_ads'] = $stats_data['expired_ads'] ?? 0;
        $stats['total_views'] = $stats_data['total_views'] ?? 0;
        $stats['total_clicks'] = $stats_data['total_clicks'] ?? 0;
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Custom Ads Management</h1>
                <p class="page-subtitle">Manage your custom advertisements and campaigns</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="custom-ad-add.php" class="btn btn-primary">
                        <i class="ri-add-line"></i>
                        <span class="hide-mobile">Add New Ad</span>
                    </a>
                    <button class="btn btn-secondary" onclick="refreshPage()">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Refresh</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-advertisement-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['total_ads']; ?></h3>
                        <p class="stat-label">Total Ads</p>
                        <span class="stat-change positive">
                            <i class="ri-arrow-up-line"></i>
                            <?php echo $stats['active_ads']; ?> Active
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-eye-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['total_views']); ?></h3>
                        <p class="stat-label">Total Views</p>
                        <span class="stat-change neutral">
                            <i class="ri-bar-chart-line"></i>
                            Impressions
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-cursor-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo number_format($stats['total_clicks']); ?></h3>
                        <p class="stat-label">Total Clicks</p>
                        <span class="stat-change positive">
                            <i class="ri-mouse-line"></i>
                            Interactions
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="ri-time-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number"><?php echo $stats['expired_ads']; ?></h3>
                        <p class="stat-label">Expired Ads</p>
                        <span class="stat-change negative">
                            <i class="ri-calendar-line"></i>
                            Need Review
                        </span>
                    </div>
                </div>
            </div>

            <!-- Ads Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">All Custom Ads</h3>
                    <div class="card-actions">
                        <div class="search-box">
                            <input type="text" id="adSearch" placeholder="Search ads..." class="form-control">
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table" id="adsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ad Details</th>
                                    <th>Duration</th>
                                    <th>Performance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ads as $ad): ?>
                                <tr data-ad-id="<?php echo $ad['id']; ?>">
                                    <td><?php echo $ad['id']; ?></td>
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="ad-image">
                                                <?php if (!empty($ad['image_url'])): ?>
                                                    <img src="../<?php echo $ad['image_url']; ?>" 
                                                         alt="Ad Image"
                                                         style="width: 50px; height: 50px; border-radius: 8px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div style="width: 50px; height: 50px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                        <i class="ri-image-line" style="color: #9ca3af;"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <h4 class="ad-title"><?php echo htmlspecialchars($ad['title']); ?></h4>
                                                <p class="text-muted" style="font-size: 0.75rem; margin: 0;">
                                                    <?php echo htmlspecialchars(substr($ad['text_snippet'], 0, 50)) . (strlen($ad['text_snippet']) > 50 ? '...' : ''); ?>
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-size: 0.875rem;">
                                            <div><strong>Start:</strong> <?php echo date('M j, Y', strtotime($ad['date_start'])); ?></div>
                                            <div><strong>End:</strong> <?php echo date('M j, Y', strtotime($ad['date_end'])); ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-size: 0.875rem;">
                                            <div><i class="ri-eye-line"></i> <?php echo number_format($ad['view_count']); ?> views</div>
                                            <div><i class="ri-cursor-line"></i> <?php echo number_format($ad['click_count']); ?> clicks</div>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="switch">
                                            <input type="checkbox" 
                                                   class="status-toggle" 
                                                   data-ad-id="<?php echo $ad['id']; ?>"
                                                   data-current-status="<?php echo $ad['on']; ?>"
                                                   <?php echo $ad['on'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="custom-ad-edit.php?id=<?php echo $ad['id']; ?>" 
                                               class="btn btn-sm btn-secondary" 
                                               title="Edit Ad">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger" 
                                                    onclick="deleteAd(<?php echo $ad['id']; ?>)"
                                                    title="Delete Ad">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                
                                <?php if (empty($ads)): ?>
                                <tr>
                                    <td colspan="6" class="text-center" style="padding: 3rem;">
                                        <div style="color: #6b7280;">
                                            <i class="ri-advertisement-line" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                                            <h3>No Custom Ads Found</h3>
                                            <p>Create your first custom ad to get started.</p>
                                            <a href="custom-ad-add.php" class="btn btn-primary" style="margin-top: 1rem;">
                                                <i class="ri-add-line"></i> Add New Ad
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="?page=<?php echo $i; ?>" 
                               class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<script>
// Toggle ad status
document.querySelectorAll('.status-toggle').forEach(toggle => {
    toggle.addEventListener('change', function() {
        const adId = this.dataset.adId;
        const currentStatus = parseInt(this.dataset.currentStatus);
        
        fetch('custom-ads.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=toggle_status&ad_id=${adId}&current_status=${currentStatus}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.dataset.currentStatus = data.new_status;
                // Show success message
                showToast('Ad status updated successfully', 'success');
            } else {
                this.checked = !this.checked; // Revert toggle
                showToast('Failed to update ad status', 'error');
            }
        })
        .catch(error => {
            this.checked = !this.checked; // Revert toggle
            showToast('An error occurred', 'error');
        });
    });
});

// Delete ad
function deleteAd(adId) {
    if (confirm('Are you sure you want to delete this ad? This action cannot be undone.')) {
        fetch('custom-ads.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete_ad&ad_id=${adId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelector(`tr[data-ad-id="${adId}"]`).remove();
                showToast('Ad deleted successfully', 'success');
            } else {
                showToast(data.message || 'Failed to delete ad', 'error');
            }
        })
        .catch(error => {
            showToast('An error occurred', 'error');
        });
    }
}

// Search functionality
document.getElementById('adSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#adsTable tbody tr');
    
    rows.forEach(row => {
        const title = row.querySelector('.ad-title');
        if (title) {
            const titleText = title.textContent.toLowerCase();
            row.style.display = titleText.includes(searchTerm) ? '' : 'none';
        }
    });
});

// Refresh page
function refreshPage() {
    location.reload();
}

// Toast notification
function showToast(message, type) {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        background: ${type === 'success' ? '#22c55e' : '#ef4444'};
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>

<?php include 'includes/footer.php'; ?>
